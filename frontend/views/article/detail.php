<?php

use common\helpers\ArticleDataHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\Article;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use common\models\LiveUpdate;
use common\helpers\BoardHelper;
use frontend\helpers\Ad;
use common\helpers\CourseHelper;
use common\models\Exam;
use frontend\helpers\Freestartads;
use yii\helpers\Inflector;

// utils
if (empty($article->author)) {
    $author = $article->defaultuser;
} else {
    $author = $article->author;
}
$currentUrl = Url::base(true) . Url::current();
$canonicalUrl = Url::base(true) . '/' . \Yii::$app->request->getPathInfo();
$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = $author && !empty($author->slug) ? ContentHelper::getUserProfilePic($author->slug) : '';
if ($entityName == 'exams') {
    $image = $article->exam[0]->cover_image;
} elseif ($entityName == 'college') {
    $image = $article->college[0]->logo_image;
} else {
    $image = Url::defaultCollegeLogo();
}

if (strpos($article->title, '{current-year}') !== false) { //first we check if the url contains the string 'en-us'
    $article->title = str_replace('{current-year}', date('Y'), $article->title); //if yes, we simply replace it with en
}
// meta key
$this->title = $article->title . ' - Getmyuni' ?? '';
$this->context->description = $article->meta_description ?? '';
$this->context->ogType = 'article';
$popularExamsPages = ['syllabus', 'application-form-details', 'admit-card', 'results'];
$interestedInPages = ['important-dates', 'syllabus', 'application-form-details', 'cut-off'];
$upcomingDisciplinePages = ['application-form-details', 'admit-card', 'sample-test-paper-content', 'results'];
$assetUrl = Yii::getAlias('@getmyuniExamAsset/');

if ($category->slug == 'others') {
    $this->registerMetaTag(['name' => 'robots', 'content' => 'noindex, nofollow']);
}

if (!empty($article->cover_image)) {
    $this->registerMetaTag(['property' => 'og:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'og:image:height', 'content' => '667']);
    $this->registerMetaTag(['property' => 'og:image:alt', 'content' => $article->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:alt', 'content' => $article->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:type', 'content' => 'image/jpeg']);
    $this->registerMetaTag(['property' => 'twitter:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'twitter:image:height', 'content' => '667']);
    $this->registerLinkTag(['href' => ArticleDataHelper::getImage($article->cover_image),  'fetchpriority' => 'high', 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => ArticleDataHelper::getImage($article->cover_image) . ' 300w', 'imagesizes' => '50vw']);
    $this->context->ogImage = ArticleDataHelper::getImage($article->cover_image);
}
if (!empty($authorImage)) {
    $this->registerLinkTag(['href' => $authorImage, 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => $authorImage . ' 300w', 'imagesizes' => '50vw']);
}
$this->registerMetaTag(['name' => 'robots', 'content' => 'max-image-preview:large']);
$this->registerMetaTag(['property' => 'article:published_time', 'content' => !empty($article->published_at) ? date(DATE_ATOM, strtotime($article->published_at)) : date(DATE_ATOM, strtotime($article->created_at))]);

$this->registerMetaTag(['property' => 'article:modified_time', 'content' => date(DATE_ATOM, strtotime($article->updated_at))]);
// breadcrumb

$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
if ($category->slug == 'boards' && !empty($article->board[0]->slug)) {
    $this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Boards'), 'url' => Url::toBoardDetail($article->board[0]->slug), 'title' => ' Articles'];
} 

if ($article->lang_code != 1) {
    $this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Articles'), 'url' => Url::toArticles(DataHelper::getLangCode($article->lang_code)), 'title' => ' Articles'];
} else {
    if ($category->slug == 'boards') {
        $boardUrl = Url::base(true) . '/boards/articles';
        // dd($boardUrl);
        $this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Article'), 'url' =>  $boardUrl, 'title' => 'Articles'];
    } else {
        $this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Articles'), 'url' => Url::toArticles(), 'title' => 'Articles'];

    }

}
if ($category->name != 'Others' && $category->slug != 'boards') {
    $this->params['breadcrumbs'][] = ['label' => $category->name, 'url' => Url::toArticleDetail($category->slug, DataHelper::getLangCode($article->lang_code)), 'title' => $category->name . ' Articles'];
}

$autoPopUpTexts = array_filter(array_column($dynamicCta, 'auto_pop_up_text'));
$finalAutoPopUpText = !empty($autoPopUpTexts) ? array_values($autoPopUpTexts)[0] : '';

$this->params['breadcrumbs'][] = ContentHelper::htmlDecode(stripslashes($article->h1), false);
$this->params['entity_name'] = addslashes($article->title) ?? '';
$this->params['entity_id'] = $article->id ?? 0;
$this->params['entity'] = Article::ENTITY_ARTICLE;
$this->params['entitySlug'] = $entitySlug ?? $article->slug;
$this->params['product_mapping_entity'] = empty($entityName) ? 'articles' : $entityName;
$this->params['product_mapping_entity_id'] = empty($entityId) ? 0 : $entityId;
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = empty($entityDisplayName) ? $article->title : $entityDisplayName;
$this->params['canonicalUrl'] = $canonicalUrl;
$this->params['streamWebengage'] = $article->stream_id ? $article->stream->name : null;
$this->params['levelWebengage'] = $article->highest_qualification ? $article->degree->name : null;
if (!empty($article->exam[0])) {
    $this->params['examWebengage'] = $article->exam[0]->name;
} else {
    $this->params['examWebengage'] = null;
}

// $this->params['amphtml'] = str_replace('/articles', '/amp/articles', $canonicalUrl);
$this->params['entity_sub_type'] = 'detail';
$this->params['auto_pop_up_text'] = $finalAutoPopUpText;

if (!empty($dynamicCta) && !empty($dynamicCta['cta_position_4'])) {
    $this->params['auto_popup_form_title'] = $dynamicCta['cta_position_4']['lead_form_title'];
}

//code for translation of article
if (!empty($translation_data)) {
    if (!empty($translation_data[0]['lang'])) {
        $msg = 'Switch to Hindi';
    } else {
        $msg = 'Switch to English';
    }
    $url = Url::toArticleDetail($translation_data[0]['slug'], $translation_data[0]['lang']);
    if (!empty($translation_data[0]['lang']) && $translation_data[0]['lang'] == 'hi') {
        $this->registerLinkTag(['href' =>  Url::base(true) . $url, 'rel' => 'alternate', 'hreflang' => $translation_data[0]['lang']]);
    } elseif (!empty($translation_data[0]['cu_lang']) && $translation_data[0]['cu_lang'] == 'hi') {
        $this->registerLinkTag(['href' =>  Url::base(true) . '/hi' . $url, 'rel' => 'alternate', 'hreflang' => 'hi']);
    }
} else {
    $url = '#';
}
$urlEn = Url::toArticleDetail($article->slug, '');
$defaulturl = Url::toArticleDetail($article->slug, '');
$this->registerLinkTag(['href' =>  Url::base(true) . $urlEn, 'rel' => 'alternate', 'hreflang' => 'en']);
$this->registerLinkTag(['href' =>  Url::base(true) . $defaulturl, 'rel' => 'alternate', 'hreflang' => 'x-default']);
$this->registerLinkTag(['rel' => 'amphtml', 'href' => !empty($article) ? Url::toAmp(Url::toArticleDetail(Inflector::slug($article->slug), DataHelper::getLangCode($article->lang_code))) : '']);

if (!empty($Chartdata)) {
    $this->registerJsFile('https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.1.4/Chart.min.js', ['depends' => [AppAsset::class]]);
    $this->params['chartData'] = $Chartdata;
}
// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'article-detail.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'bottom-widget.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'exam_download_resource.css', ['depends' => [AppAsset::class]]);

$courseState = json_encode([]);
if (!empty($stateList)) {
    $this->params['stateList'] = $stateList;
    if (isset($stateList['data']) && !empty($stateList['data'])) {
        $courseState = json_encode(array_keys($stateList['data']));
    }
}
// schema
if (!empty($faqs)) {
    foreach ($faqs as $faq) {
        $loadFaq[] = [
            '@type' => 'Question',
            'name' => ContentHelper::htmlDecode($faq->question, true),
            'acceptedAnswer' => [
                '@type' => 'Answer',
                'text' => ContentHelper::htmlDecode($faq->answer, false)
            ]
        ];
    }
    $this->params['schema1'] = \yii\helpers\Json::encode([[
        '@context' => 'http://schema.org',
        '@type' => 'FAQPage',
        'mainEntity' => $loadFaq
    ]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
}

$this->params['schema'] = \yii\helpers\Json::encode([[
    '@context' => 'http://schema.org',
    '@type' => 'Article',
    'mainEntityOfPage' => [
        '@type' => 'WebPage',
        '@id' => $canonicalUrl,
    ],
    'headline' => $this->title,
    'image' => [ArticleDataHelper::getImage($article->cover_image)],
    'datePublished' => !empty($article->published_at) ? date(DATE_ATOM, strtotime($article->published_at)) : date(DATE_ATOM, strtotime($article->created_at)),
    'dateModified' => date(DATE_ATOM, strtotime($article->updated_at)),
    'author' => [
        '@type' => 'Person',
        'name' => $article->author ? $article->author->name : ''
    ],
    'publisher' => [
        '@type' => 'Organization',
        'name' => 'Getmyuni',
        'logo' => [
            '@type' => 'ImageObject',
            'url' => Yii::$app->params['gmuLogo']
        ],
    ],
    'description' => $article->meta_description
]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

$downloadableResourceSection = '';
if (!empty($downloadableResource)) {
    $downloadableResourceSection = '<div class="exam-download-section" data-exam-id="' . $exam->id . '" data-stream-id="' . $exam->primary_stream_id . '" data-entity="exam"></div>';
}


$h2Text = ContentHelper::getGenerateHtml($article->description, '', $downloadableResourceSection);
$article->description = $h2Text['content'];
?>
<div class="containerMargin">
    <div class="row">
        <div class="col-md-12">
            <div class="articleHeader">
                <section class="pageDescription">
                    <div class="row">
                        <div class="col-md-12">
                            <!--span class="spriteIcon liveIcon"></span-->
                            <h1><?= ContentHelper::htmlDecode(stripslashes($article->h1), false) ?></h1>
                            <div class="authorInfoAndTranslateBtn">
                                <div class="updated-info row">

                                    <?php /*<img class="lazyload" loading="lazy" width="60" height="60" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author ? $author->name . ' Image' : '' ?>" />*/ ?>
                                    <span class="updatedDetails">
                                        <?php if ($author): ?>
                                            <?php
                                            $about = $author->profile->about ?? '';
                                            $authorName = $author->name ?: ucfirst(str_replace('-', ' ', $author->username));
                                            $authorUrl = ($article->lang_code != 1) ? '/hi/author/' . $author->slug : '/author/' . $author->slug;
                                            ?>
                                            <div class="updatedBy">
                                                <p>
                                                    <?php if (!empty($about)): ?>
                                                        <a href="<?= $authorUrl ?>"><?= $authorName ?></a>,
                                                    <?php else: ?>
                                                        <span><?= $authorName ?></span>,
                                                    <?php endif; ?>
                                                </p>
                                            </div>
                                            <p>
                                                <span>
                                                    <?=Yii::$app->formatter->asDatetime($article->updated_at, 'php:M d, Y | h:i A') ?> IST
                                                </span>
                                                <?php if (!empty($article->college[0])): ?>
                                                    | <a href="<?= Url::toCollege($article->college[0]['slug']) ?>" title=<?= $article->college[0]['display_name'] ?? $article->college[0]['name'] ?>> <?= $article->college[0]['display_name'] ?? $article->college[0]['name'] ?></a>
                                                <?php elseif (!empty($article->exam[0])): ?>
                                                    | <a href="<?= Url::toExamDetail($article->exam[0]['slug']) ?>" title=<?= $article->exam[0]['display_name'] ?? $article->exam[0]['name'] ?>><?= $article->exam[0]['display_name'] ?? $article->exam[0]['name'] ?></a>
                                                <?php elseif (!empty($article->course[0])): ?>
                                                    | <a href="<?= Url::toCourseDetail($article->course[0]['slug']) ?>" title=<?= $article->course[0]['short_name'] ?? $article->course[0]['name'] ?>> <?= $article->course[0]['short_name'] ?? $article->course[0]['name'] ?></a>
                                                <?php elseif (!empty($article->board[0])): ?>
                                                    | <a href="<?= Url::toBoardDetail($article->board[0]['slug']) ?>" title=<?= $article->board[0]['display_name'] ?? $article->board[0]['name'] ?>> <?= $article->board[0]['display_name'] ?? $article->board[0]['name'] ?></a>
                                                <?php endif; ?>
                                        <?php endif; ?>
                                            </p>
                                            <ul>
                                                <p><?= Yii::t('app', 'Share it on') ?>:</p>
                                                <li>
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $canonicalUrl ?>" target="_blank" rel="nofollow" class="spriteIcon greyFbIcon"></a>
                                                </li>
                                                <li>
                                                    <a href="https://twitter.com/share?url=<?= $canonicalUrl ?>" class="spriteIcon greyTwitterIcon" rel="nofollow" target="_blank"></a>
                                                </li>
                                            </ul>
                                    </span>
                                </div>
                                <div class="col-md-6 cta-btn-row-fix">
                                    <div class="ctaColumn">
                                        <div class="ctaRow">
                                            <div class="lead-cta" data-entity="article" data-lead_cta='0'></div>
                                        </div>
                                    </div>
                                </div>
                                <?php
                                if (!empty($translation_data)) {
                                    ?>
                                    <a href="<?= $url ?>" class="translateBtn">
                                        <span class="webpSpriteIcon translateIcon1"></span>
                                        <?= $msg ?>
                                    </a>
                                    <?php
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <nav class="stickyNavCls">
            <?php if (!empty($menus)): ?>
                <div class="examRelataedLinks">
                    <?php if (count($menus) > 14 && !$isMobile): ?>
                        <p class="btn_left over">
                            <i class="spriteIcon left_angle"></i>
                        </p>
                    <?php endif; ?>
                    <?php if (count($menus) > 12 && !$isMobile): ?>
                        <p class="btn_right">
                            <i class="spriteIcon right_angle"></i>
                        </p>
                    <?php endif; ?>
                    <?php if (count($menus) > 3 && $isMobile): ?>
                        <!-- <p class="btn_right">
                                <i class="spriteIcon right_angle"></i>
                            </p> -->
                    <?php endif; ?>
                    <?=
                    $this->render('partials/_menu-card', [
                        'menus' => $menus,
                        'entityName' => $entityName,
                        'entitySlug' => $entitySlug,
                        'entityDisplayName' => $entityDisplayName,
                        'stateId' => !empty($college->city) ? $college->city->state->old_id : '',
                        'interestedLocation' => !empty($college->city) ? $college->city->id : '',
                        'allBoardPage' => !empty($allBoardPage) ? $allBoardPage : [],
                    ]);
                    ?>
                </div>
            <?php endif; ?>
        </nav>
        <div class="col-md-8">
            <main>
                <article>
                    <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                        <div class="horizontalRectangle">
                            <div class="appendAdDiv" style="<?= $isMobile ? 'height: 50px;' : '' ?>background:#EAEAEA;">
                                <?php /* if ($isMobile) : ?>

                                <?php else : */ ?>
                                <?php echo Ad::unit('GMU_ARTICLES_LANDING_WEB_728x90_ATF', '[728,90]') ?>
                                <?php //endif;
                                ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="articelNote">
                        <p><?= $article->meta_description ?></p>
                    </div>
                    <?php if (!empty($article->cover_image)): ?>
                        <div class="bannerImg">
                            <picture>
                                <source media="(max-width: 500px)" srcset="<?= ArticleDataHelper::getImage($article->cover_image) ?>">
                                <img width="1200" height="675" src="<?= ArticleDataHelper::getImage($article->cover_image) ?>" alt="<?= $article->h1 ?>" />
                            </picture>
                        </div><br />
                    <?php endif; ?>
                    <?php if (!empty($article->audio)): ?>
                        <div class="audio-container">
                            <span class="audio-text"><?= Yii::t('app', 'Listen to this article') ?></span>
                            <audio controls id="myAudio" preload="none">
                                <source src="<?php echo $article->audio ?>" type="audio/mpeg">
                                Your browser does not support the audio element.
                            </audio>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($article->articleSubpage)) { ?>
                        <div class="articleInfo pageInfo">
                    <?php } else { ?>
                            <div class="articleInfo">
                    <?php } ?>
                            <?php
                            // $createdDate = new DateTime($article->created_at);
                            // $comparisonDate = new DateTime('2024-09-03 12:00:00');
                            // if (isset($h2Text['h2']) && !empty($h2Text['h2']) && $createdDate > $comparisonDate):
                            if (isset($h2Text['h2']) && !empty($h2Text['h2'])):
                                ?>
                                <?= $this->render('partials/_table-of-content', [
                                    'h2Content' => $h2Text['h2'],
                                    'lang' => Yii::$app->language,
                                ]); ?>
                            <?php endif; ?>
                            <?php
                            if (!empty($recentActivity)): ?>
                                <?= $this->render('../partials/_recentActivity', [
                                    'recentActivity' => $recentActivity,
                                ]) ?>
                            <?php endif;
                            if (!empty($board)):
                                ?>
                                <?=
                                ContentHelper::removeStyleTag(stripslashes(
                                    html_entity_decode(DataHelper::parseDomainUrlInContent(BoardHelper::parseBoardUrl($article->description, $board->slug)))
                                )) ?>
                            <?php else: ?>
                                <?=
                                ContentHelper::removeStyleTag(stripslashes(
                                    html_entity_decode(DataHelper::parseDomainUrlInContent($article->description))
                                )) ?>
                            <?php endif;
                            if (!empty($tags)): ?>
                                <div class="tagsDiv">
                                    <ul>
                                        <li><?= Yii::t('app', 'TAGS') ?></li>
                                        <?php foreach ($tags as $tag): ?>
                                            <li><a href="<?= Url::toTag($tag->slug) ?>" title="<?= $tag->name ?>"><?= strtoupper($tag->name) ?></a></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            </div>
                            <?php
                            $i = 0;
                            if (!empty($articleSection)):
                                foreach ($articleSection as $key => $section):
                                    ?>
                                    <div class="gmu-mock-panel">
                                        <h1 class="gmu-mock-heading"><?= $key; ?></h1>
                                        <!--div class="pageDataSection <?php //if (str_word_count($articleSection[$key]['description']) >250) {
                                                                        //echo 'pageInfo';
                                        ?>">
                        <p class=""> //$articleSection[$key]['description']; </p>
                        </div>
                        <ul class="gmu-testList"-->
                                        <?php foreach ($section as $keyTopic => $topic):
                                            if ($keyTopic == 'description') {
                                                continue;
                                            } ?>
                                            <li class="gmu-testListItem">
                                                <div class="gmu-testList-left">
                                                    <h2><?= $topic[0] ?></h2>
                                                    <a class="gmu-testList-applyBtn" href="<?= $keyTopic ?>" title="<?= $topic[0] ?> Important Questions with Answers">
                                                        Attempt Now &nbsp;
                                                        <span class="spriteIcon rightArw"></span>
                                                    </a>
                                                </div>
                                            </li>
                                        <?php endforeach; ?>
                                        <li class="gmu-testList-viewBtnCtn">
                                            <a class="gmu-readMore-btn" id="gmu-viewAll-btn" href="<?= $topic[2] ?>" title="<?= $key ?>">VIEW MORE</a>
                                        </li>
                                        </ul>
                                    </div>

                                    <?php $i++;
                                endforeach;
                            endif;
                            ?>

                </article>

                <!-- faq section -->
                <?php if (!empty($faqs)): ?>
                    <section class="faq_section">
                        <?php if (empty($article->display_name)) { ?>
                            <h2><?= Yii::t('app', 'FAQs') ?></h2>
                        <?php } else { ?>
                            <h2><?= Yii::t('app', ' FAQs on ' . $article->display_name) ?></h2>
                        <?php } ?>
                        <div class="faqDiv">

                            <?php foreach ($faqs as $faq): ?>
                                <div>
                                    <p class="faq_question"><?= 'Q: ' . $faq->question ?></p>
                                    <div class="faq_answer" style="display: none;">
                                        <?= 'A: ' . ContentHelper::htmlDecode($faq->answer, false) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                        </div>
                    </section>
                <?php endif; ?>
            </main>
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_siderail_right_2', '__200x600')
                            ?>
                        <?php else: ?>
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280')
                            ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <div class="col-md-4">
            <aside class="article-aside">
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="getSupport">
                        <!--div class="row">
                            <img width="80" height="80" class="lazyload" loading="lazy" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="" />
                            <p><!-?= //Yii::t('app', 'Get Expert Counseling and Student Scholarship') ?></p>
                        </div-->
                        <!-- <p class="getSupport__subheading"><?= Yii::t('app', 'Get Expert Counseling and Student Scholarship') ?></p> -->
                        <div class="button__row__container">
                            <div class="lead-cta" data-entity="article" data-lead_cta='1'></div>
                        </div>
                    </div>
                <?php endif;
                if (!empty($upcomingExams)): ?>
                    <div class="trendingArtilce trendingArtilerList">
                        <p><?= Yii::t('app', 'UPCOMING EXAMS') ?></p>

                        <?php $upExamsCount = 0; ?>
                        <?php foreach ($upcomingExams as $upExams): ?>
                            <?php if ($upExamsCount == 6) {
                                break;
                            }
                            $upExamsCount++; ?>
                            <div class="trendingArtilerDiv row">
                                <div class="dateLabel">
                                    <span><?= Yii::$app->formatter->asDate(substr($upExams->start, 0, 10), 'd') ?></span>
                                    <?= Yii::$app->formatter->asDate(substr($upExams->start, 0, 10), 'php:M') ?>
                                </div>
                                <div class="trendingArtileText">
                                    <a href="<?= Url::toExamDetail($upExams->exam->slug) ?>" title="<?= $upExams->exam->display_name ?? $upExams->exam->name ?>"><?= $upExams->exam->display_name ?? $upExams->exam->name ?></a>
                                </div>
                            </div>
                        <?php endforeach; ?>

                    </div>
                <?php endif; ?>
                <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>*/
                if (!empty($recentArticles) || !empty($trendings)): 
                    if(!str_starts_with(\Yii::$app->request->getPathInfo(), 'boards/articles/')):
                ?>
                        <?= $this->render('partials/_sidebar-articles', [
                            'trendings' => $trendings,
                            'recentArticles' => $recentArticles,
                            'article' => $article
                        ]); 
                        ?>
                <?php else: ?>
                        <?= $this->render('partials/_sidebar-articles', [
                            'trendings' => $trendings,
                            'recentArticles' => $recentArticles,
                            'article' => $article,
                            'category' => $category
                        ]); ?>
                <?php endif; endif; ?>

                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="squareDiv">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Ad::unit('GMU_ARTICLES_DETAIL_WAP_300x250_MTF_1', '[300,250]') ?>
                            <?php else: ?>
                                <?php echo Ad::unit('GMU_ARTICLES_DETAIL_WEB_300x250_MTF_1', '[300,250]') ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif;
                if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="verticleRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php echo Freestartads::unit('getmyuni-com_siderail_right_2', '__300x600')
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if (!empty($coursebyStream) && !$isMobile): ?>
                    <?= $this->render('partials/_course-list', [
                        'title' => 'Other Popular ' . $streamName . ' Courses',
                        'lists' => $coursebyStream,
                        'class' => 'desktopOnly',

                    ]) ?>
                <?php endif; ?>
                <?php if (!empty($topicArticleLink)): ?>
                    <div class="quickLinks article-links">
                        <h2><?= Yii::t('app', 'Important Questions') ?></h2>
                        <ul>
                            <?php foreach ($topicArticleLink as $key => $value):
                                foreach ($value as $keyVal => $val): ?>
                                    <li>
                                        <a title="<?= $val ?>" href="<?= Url::toArticleDetail($keyVal, '') ?>">
                                            <?= $val ?>
                                        </a>
                                    </li>
                                <?php endforeach;
                            endforeach; ?>
                        </ul>
                    </div>
                <?php endif ?>
                <?php if (count($pages) > 1): ?>
                    <div class="quickLinks">
                        <h2><?= Yii::t('app', 'Quick Links') ?></h2>
                        <ul>
                            <?php foreach (DataHelper::examContentList() as $key => $value): ?>
                                <?php if (in_array($key, $menus)): ?>
                                    <li>


                                        <a title="<?= $exam->display_name . ' ' . ($key == 'overview' ? '' : $value) ?>" href="<?= $key == 'overview' ? Url::toExamDetail($exam->slug) : Url::toExamDetail($exam->slug, '', $key) ?>">
                                            <?= $exam->display_name . ' ' . $value ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif ?>
                <?php if (!empty($coursebyStream) && $isMobile): ?>
                    <?= $this->render('partials/_course-list', [
                        'title' => 'Other Popular ' . $streamName . ' Courses',
                        'lists' => $coursebyStream,
                    ]) ?>
                <?php endif; ?>
                <?php if (!empty($courseSpecialization) && !$isMobile): ?>
                    <?= $this->render('partials/_course-list', [
                        'title' => $parentCourseName . ' Specialization',
                        'lists' => $courseSpecialization ?? [],
                        'class' => 'desktopOnly',
                    ]) ?>
                <?php endif; ?>
                <?php if (!empty($courseSpecialization) && $isMobile): ?>
                    <?= $this->render('partials/_course-list', [
                        'title' => $parentCourseName . ' Specialization',
                        'lists' => $courseSpecialization ?? [],
                    ]) ?>
                <?php endif; ?>
            </aside>
        </div>
    </div>

    <?php /* if ($author->slug): ?>
    <div class="contentProvider">
        <div class="profilePic">
            <img class="lazyload" loading="lazy" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author->name . ' Image' ?>" />
        </div>
        <div class="profileInfo">
            <p class="name"><?= $article->author->name ?></p>
            <p class="position"><?= isset($article->author->profile->job_role) ? $article->author->profile->job_role . ',' : '' ?> GETMYUNI</p>
            <p><?= isset($article->author->profile->about) ? $article->author->profile->about : '' ?></p>
        </div>
    </div>
<?php endif;*/ ?>
    <section class="commentSection">
        <?= $this->render('/partials/comment/_form', [
            'model' => $commentModel,
            'entity' => Article::ENTITY_ARTICLE,
            'entity_id' => $article->id
        ]) ?>
        <?= $this->render('/partials/comment/_comment', [
            'comments' => $comments,
            'entity' => Article::ENTITY_ARTICLE,
            'entityId' => $article->id
        ]) ?>
    </section>
    <!-- reviews widget -->
    <?php if (!empty($reviews)): ?>
        <?= $this->render('partials/_review-card', [
            'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
            'reviews' => $reviews,
            'rating' => $revRating,
            'distrbutionRating' => $revDistributionRating,
            'categoryRating' => $revCategoryRating,
            'viewAllURL' => Url::toCollege($college->slug, 'reviews')
        ]) ?>
    <?php endif; ?>
    <?php
    if (!empty($liveApplicationForm) && !$isMobile): ?>
        <?=
        $this->render('partials/_live-application-desktop', [
            'liveApplicationForm' => $liveApplicationForm,
            'entityName' => $entityName,
            'assetUrl' => DataHelper::s3Path(null, 'azure_college_image', 'path') . '/',
            'entityDisplayName' => $entityDisplayName,
        ])
        ?>
    <?php endif; ?>
    <?php if (!empty($liveApplicationForm) && $isMobile): ?>
        <?=
        $this->render('partials/_live-application-mobile', [
            'liveApplicationForm' => $liveApplicationForm,
            'entityName' => $entityName,
            'assetUrl' => DataHelper::s3Path(null, 'azure_college_image', 'path') . '/',
            'entityDisplayName' => $entityDisplayName,
        ])
        ?>
    <?php endif; ?>
    <!-- college widget -->
    <?php if (!empty($stateColleges) && !str_starts_with(\Yii::$app->request->getPathInfo(), 'boards/articles/')): ?>
        <?= $this->render('../board/partials/_colleges-card', [
            'stateColleges' => $stateColleges,
            'board' => $board,
            'assetUrl' => $assetUrl
        ]) ?>
    <?php endif; ?>

    <!-- exam widget -->
    <?php if (!empty($boardExamList)): ?>
        <?= $this->render('../board/partials/_exam-card', [
            'exams' => $boardExamList,
            'board' => $board,
            'assetUrl' => $assetUrl
        ]) ?>
    <?php endif; ?>
    <!-- Board Article  -->
    <?php if (!empty($boardarticles) && (\Yii::$app->request->getPathInfo() != 'boards/articles/')): ?>
        <?= $this->render('../board/partials/_board-articles', [
            'articles' => $boardarticles,
            'board' => $board,
        ]) ?>
    <?php else: ?>
        <?= $this->render('../board/partials/_board-articles', [
            'articles' => $boardarticles,
            'board' => $board,
            'category' => $category,
            'boardUrl' => (\Yii::$app->request->getPathInfo() != 'boards/articles/') ? 'boards/articles' : '',
        ]) ?>
    <?php endif; ?>
    <!-- Board News  -->
    <?php if (!empty($boardExams)): ?>
        <?= $this->render('../board/partials/_board-news', [
            'boardExamNews' => $boardExams,
            'board' => $board,
        ]) ?>
    <?php endif; ?>
    <?php if (!empty($courseCollege) && $courseCollege['items']): ?>
        <?= $this->render('partials/_college-list', [
            'collegeCard' => $courseCollege['items'],
            'stream' => $course,
            'is_sponsored' => $courseCollege['is_sponsored'],
        ]); ?>
    <?php endif; ?>

    <?php
    if (!empty($courseExams)):  ?>
        <?= $this->render('partials/_exam-list', [
            'examCard' => $courseExams,
            'title' => 'Top ' . $streamName . ' Entrance Exams',
            'stream' => $course->stream
        ]); ?>
    <?php endif; ?>
    <?php if (!empty($Chartdata)): ?>
        <div class="pageData">
            <h2><?= $course->short_name ?> <?= Yii::t('app', 'Fee Structure') ?></h2>
            <div class="chartContainer">
                <canvas class="chartDiv" id="chartDiv" width="400" height="200"></canvas>
            </div>
        </div>
    <?php endif; ?>
    <?php if ($showOtherCourseCategory): ?>
        <section class="pageData ohterCategoryArticles">
            <h2><?= Yii::t('app', 'Other Category Courses') ?></h2>
            <div class="otherCategorySection">
                <i class="spriteIcon scrollLeft over"></i>
                <i class="spriteIcon scrollRight"></i>
                <div class="row">
                    <?php foreach (CourseHelper::$categoryArr as $key => $value): ?>
                        <div class="categoryArticles">
                            <a href="<?= Url::toCoursesStream($key) ?>">
                                <div class="categoryArticlesImg">
                                    <span class="courseSprite <?= $key ?>"></span>
                                </div>
                                <p><?= $value ?></p>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <?php if (!empty($stateList['data'])): ?>
        <div class="pageData">
            <h2><?= Yii::t('app', 'See Various ') ?><?= $course->short_name ?> <?= Yii::t('app', 'Colleges in India') ?></h2>
            <div id="vmap" style="width: auto; <?= (!$isMobile) ? 'height:600px' : 'height:358px' ?> "></div>
        </div>
        <?php
        echo $this->registerJs(
            "if (gmu.config.mapData.data.length !== 0) {
            let url =  gmu.config.mapData.courseSlug + '-colleges';
            $(document).ready(function () {
                jQuery('#vmap').vectorMap({
                    map: 'india_en',
                    backgroundColor: 'white',
                    borderColor: '#ffffff',
                    borderOpacity: 1,
                    borderWidth: 1,
                    color: '#d9d9d9',
                    enableZoom: false,
                    hoverColor: '#0a66c2',
                    showTooltip: true,
                    scaleColors: ['#C8EEFF', '#006491'],
                    normalizeFunction: 'polynomial',
                    selectedColor: '#6dabf5',
                    selectedRegions: $courseState,
                    onRegionClick: function (element, code, region)
                    {  
                        if (jQuery.inArray(code, $courseState) != -1) {
                            window.location.href = window.location.origin + '/' + url + '/' + code;
                        }
                    }
                });
            });
        }"
        );
    endif; ?>
    <!-- other colleges under university -->
    <?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Other Colleges under ' . (!empty($parentCollege->display_name) ? $parentCollege->display_name : $parentCollege->name),
            'colleges' => $affiliatedCollege,
        ]) ?>
    <?php endif; ?>
    <!-- gallery -->
    <?php if (!empty($gallery)): ?>
        <div class="pageData photoGallery">
            <h2 class="row"><?= Yii::t('app', 'Gallery') ?> <a href="<?= Url::toCollege($college->slug, 'images-videos') ?>"><?= Yii::t('app', 'View All') ?></a></h2>
            <div class="row">
                <?php foreach ($gallery as $image): ?>
                    <div class="picture">
                        <img class="lazyload" width="273" height="206" loading="lazy" data-src="<?= Url::getCollegeImage($college->slug, $image->file) ?>" src="<?= Url::getCollegeImage($college->slug, $image->file) ?>" alt="">
                    </div>
                <?php endforeach; ?>

                <?php if ($isMobile): ?>
                    <div class="picture mobileOnly">
                        <div class="viewAllDiv">
                            <a href="<?= Url::toCollege($college->slug, 'images-videos') ?>"><i class="spriteIcon viewAllIcon"></i><?= Yii::t('app', 'View All') ?></a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- contact us -->

    <?php if (!empty($collegeExams)): ?>
        <?= $this->render('partials/_college-exam', [
            'title' => 'Exams Accepted by ' . (!empty($college->display_name) ? $college->display_name : $college->name),
            'collegeExams' => $collegeExams
        ]) ?>
    <?php endif; ?>

    <!-- colleges accepting exams -->
    <?php if (!empty($collegeAcceptingExams['college']) && count($collegeAcceptingExams['college'])): ?>
        <?= $this->render('/exam/partials/_college-accepting-exam', [
            'collegeAcceptingExams' => $collegeAcceptingExams,
            'exam' => $article->exam[0]
        ])
        ?>
    <?php endif; ?>

    <?php if (!empty($intrestedExams)): ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => $article->exam[0]->slug,
            'title' => Yii::t('app', 'YOU MAY ALSO BE INTERESTED IN'),
            'totalCards' => 4,
            'pageSlugCount' => 4,
            'pages' => $interestedInPages,
            'details' => $intrestedExams,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
        ])
        ?>
    <?php endif; ?>
    <?php if (!empty($upcomingExamByStream)): ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => $article->exam[0]->slug,
            'title' => Yii::t('app', 'Upcoming') . ' ' . $primaryStream->name . ' ' . Yii::t('app', 'Exams'),
            'totalCards' => 4,
            'pageSlugCount' => 4,
            'pages' => $upcomingDisciplinePages,
            'details' => $upcomingExamByStream,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
            'viewAllUrl' => Url::toDisciplineExam($primaryStream->slug),
            'viewAllTitle' => $streams[0]['name'] . ' Entrance Exams in India'
        ])
        ?>
    <?php endif; ?>
    <?php if (!empty($popularExams)): ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => '',
            'title' => Yii::t('app', 'Popular Exams'),
            'totalCards' => 4,
            'pageSlugCount' => 4,
            'details' => $popularExams,
            'pages' => $popularExamsPages,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
        ])
        ?>
    <?php endif; ?>

    <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
        <?= $this->render('partials/_similar-college-card', [
            'collegeByDiscipline' => $collegeByDiscipline['colleges'],
            'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
            'college' => $college
        ]) ?>
    <?php endif; ?>

    <!-- Nearby colleges -->
    <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
        <?= $this->render('partials/_college-card', [
            'title' => Yii::t('app', 'Explore Nearby Colleges'),
            'colleges' => $nearByCollege,
            'viewAllUrl' => "{$college->city->slug}",
        ]) ?>
    <?php endif; ?>

    <!-- related Articles and News -->
    <?php
    if ((empty($article->college) && empty($article->exam) && empty($article->course) && empty($article->board)) || !empty($productArticle) || !empty($productNews)):
        $productArticlesToRender = !empty($productArticle) && count($productArticle) > 1 ? $productArticle : null;
        $relatedArticlesToRender = !empty($article->article) ? $article->article : $relatedArticles;
        // $newsToRender = !empty($productNews) ? $productNews : (!empty($article->news) ? $article->news : null);
        $newsToRender = !empty($recentStreamNews) ? $recentStreamNews : null;
        if (!empty($productArticlesToRender) || !empty($relatedArticlesToRender) || !empty($newsToRender)): ?>
            <?= $this->render('../partials/_relatedArticleNews', [
                'relatedArticles' => $productArticlesToRender ? $productArticlesToRender : $relatedArticlesToRender,
                'news' => $newsToRender,
                'article' => $article
            ]); ?>
        <?php endif; ?>

    <?php endif; ?>

    <div id="comment-reply-form-js"></div>
</div>
<?php
$this->registerJsFile(Yii::$app->params['jsPath'] . 'map.js', ['depends' => [AppAsset::class]]);
?>