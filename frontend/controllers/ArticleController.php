<?php

namespace frontend\controllers;

use common\models\Article;
use common\models\Category;
use common\models\CategoryTranslation;
use common\models\Course;
use common\models\College;
use common\models\Exam;
use common\services\FaqService;
use common\services\StudyAbroadService;
use frontend\helpers\Ad;
use frontend\helpers\Url;
use frontend\models\CommentForm;
use frontend\services\ArticleService;
use frontend\services\ExamService;
use common\services\ExamService as CommonExamService;
use common\services\CollegeService;
use common\services\CourseService;
use yii\caching\TagDependency;
use Yii;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\helpers\ArrayHelper;
use common\services\v2\NewsService;
use common\helpers\DataHelper;
use common\helpers\ArticleDataHelper;
use common\models\Board;
use common\models\Stream;
use common\models\Degree;
use common\helpers\BoardHelper;
use common\services\UserService;
use common\models\LeadBucketTagging;
use common\services\BoardService;
use common\models\UserTranslation;
use common\services\WebEngage;
use yii\web\HttpException;
use yii\web\ServerErrorHttpException;
use Exception;
use common\models\ArticleSubpage;
use common\models\ArticleSubpageSection;
use common\models\ArticleSubpageSubsection;
use common\models\ArticleSubpageSubsectionQuesAns;
use common\models\ArticleSubpageSubsectionSubtopic;

class ArticleController extends Controller
{
    protected $articleService;
    protected $studyAbroadService;
    protected $faqService;
    protected $examService;
    protected $collegeService;
    protected $courseService;
    protected $commonExamService;
    protected $newsService;
    protected $boardService;
    public $pageType;
    public $entityType;


    public function __construct(
        $id,
        $module,
        ArticleService $articleService,
        StudyAbroadService $studyAbroadService,
        FaqService $faqService,
        ExamService $examService,
        CommonExamService $commonExamService,
        CollegeService $collegeService,
        CourseService $courseService,
        NewsService $newsService,
        BoardService $boardService,
        $config = []
    ) {
        $this->articleService = $articleService;
        $this->boardService = $boardService;
        $this->studyAbroadService = $studyAbroadService;
        $this->faqService = $faqService;
        $this->examService = $examService;
        $this->collegeService = $collegeService;
        $this->courseService = $courseService;
        $this->newsService = $newsService;
        $this->commonExamService = $commonExamService;
        parent::__construct($id, $module, $config);
    }

    public function actionIndex($category = null)
    {
        $response = DataHelper::trackStudentActivity('article-index');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];

        if (Url::hasParameters()) {
            throw new NotFoundHttpException();
        }

        $cate = Category::find()->where(['slug' => $category])->one();

        if (empty($category)) {
            $latestArticles = $this->articleService->getAll(4, ['category', 'author'], Category::EXCLUDE_CATEGORY, 'published_at');
            $byCategories = $this->articleService->postsByCategories(Category::EXCLUDE_CATEGORY);
            $trendings = $this->articleService->getTrendingArticleWithAuthor();
            $indexTitle = 'Find Latest Articles on Colleges, Exams, Courses, Boards & more - Getmyuni';
            $indexDescription = 'Find articles by Getmyuni - Renowned source of free, accurate, & insightful college admissions, exam updates and student mentorship information.';
        } else {
            $latestArticles = $this->articleService->getAllCategoryArticle(4, ['category', 'author'], 'published_at', '', $cate->id ?? '');
            $trendings = $this->articleService->getCategoryTrendingArticleWithAuthor($cate->id ?? '');
            $byCategories = $this->articleService->postByEntity($cate);
            $indexTitle = 'Board Exam Articles – CBSE, ICSE, UP, Bihar, MP & State Boards Updates';
            $indexDescription = 'Explore the latest board exam articles, updates, and guides for CBSE, ICSE, UP Board, Bihar Board, MP Board, Maharashtra Board and other state boards.';
        }

        $data = [
            'latestArticles'  => $latestArticles,
            'byCategories' => $byCategories,
            'trendings' => $trendings,
            'cate' => $cate,
            'indexTitle' => $indexTitle,
            'indexDescription' => $indexDescription

        ];

        return $this->render('index', $data);
    }

    public function actionRoute($slug, $category = '')
    {
        if ($slug == 'others') {
            throw new NotFoundHttpException();
        }
        $lang_code = DataHelper::getLangId();
        $category = Category::find()->bySlug($slug)->active()->one();
        if (!empty($category)) {
            $categories_translation = CategoryTranslation::find()->select(['lang_code', 'display_name'])->where(['category_id' => $category->id])->andWhere(['lang_code' => $lang_code])->one();
            if (!empty($categories_translation)) {
                $category->name = $categories_translation->display_name;
                $category->lang_code = $categories_translation->lang_code;
            } else {
                $category->lang_code = 1;
            }
            $response = DataHelper::trackStudentActivity('article-route-category');
            $this->entityType = $response['entityType'];
            $this->pageType = $response['pageType'];
            return $this->showCategory($category);
        }

        $article = $this->articleService->getDetail($slug);
        if (!empty($article) && $slug == $article->slug) {
            $response = DataHelper::trackStudentActivity('article-route-detail');
            $this->entityType = $response['entityType'];
            $this->pageType = $response['pageType'];
            return $this->show($article);
        }

        // Article Topic Page

        $articleSubSection  = $this->articleService->getArticleSubpageSection($slug);
        if (!empty($articleSubSection)) {
            $article = $this->articleService->getDetail($articleSubSection->article_id);
            $response = DataHelper::trackStudentActivity('article-route-detail');
            $this->entityType = $response['entityType'];
            $this->pageType = $response['pageType'];
            return $this->showSection($article, $articleSubSection->id);
        }

        $articleSubSectionTopic  = $this->articleService->getArticleSubpageSectionTopic($slug);
        if (!empty($articleSubSectionTopic)) {
            $article = $this->articleService->getDetail($articleSubSectionTopic->article_id);
            $response = DataHelper::trackStudentActivity('article-route-detail');
            $this->entityType = $response['entityType'];
            $this->pageType = $response['pageType'];
            return $this->showSuSection($article, $articleSubSectionTopic->article_subpage_section_id, $articleSubSectionTopic->article_subpage_section_topic_id, $articleSubSectionTopic->id);
        }

        throw new NotFoundHttpException();
    }

    public function show($article)
    {
        if (!$article) {
            throw new NotFoundHttpException();
        }

        $boardArticleUrl = str_starts_with(\Yii::$app->request->getPathInfo(), 'boards/articles/');
        if (!in_array($article->slug, ArticleDataHelper::$boardArticle) && $boardArticleUrl) {
            throw new NotFoundHttpException();
        }


        $funArguments = func_get_args();
        $funArguments['pageType'] = 'article-detail';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        $topicArticleLink=[];
        // yii::$app->cache->flush();
        //  Yii::$app->cache->delete($key);
        // $data = Yii::$app->cache->getOrSet($key, function () use ($article) {
        if ($data = $this->articleService->getAdTargetData($article)) {
            Ad::setTargetArticleNews($data);
        }
            $tags = $article->tags;
            $recentEntity = $article['id'];
            $entityName = '';
            $entityDisplayName = '';
            $allBoardPage = [];
        if (!empty($article->audio)) {
            $getS3Audio = DataHelper::s3Path($article->audio, 'article_audio', true);
            $article->audio = $getS3Audio;
        }
            $articleSection = $this->articleService->getArticlePracticeDetail($article) ?? [];
            $relatedArticle = $this->articleService->getByCategory($article->category->id) ?? [];
            $active_trans_data = [];
        foreach ($article->activeTranslation as $trans_data) {
            $active_trans['cu_lang'] = DataHelper::getLangCode($article['lang_code']);
            $active_trans['slug'] = $trans_data['slug'];
            $active_trans['lang'] = DataHelper::getLangCode($trans_data['lang_code']);
            $active_trans_data[] = $active_trans;
        }
        if ($article->college) {
            $entityName = 'colleges';
            $college = College::find()->where(['=', 'id', $article->college[0]['id']])->one();
            if (empty($college)) {
                return [];
            }
            $entityId = $article->college[0]['id'];
            $nearByCollege = $this->collegeService->getCollegesByCity($college->city_id, 4, $college->id);
            $gallery  = $this->collegeService->getGallery($college, 4);
            $collegeByDiscipline  = $this->collegeService->getCollegeByDiscipline($college->id, $college->city->id);
            $entityDisplayName = $article->college[0]['name'];
            $entityId = $article->college[0]['id'];
            $menus = $this->collegeService->getMenu($article->college[0]);
            $entitySlug = $article->college[0]['slug'];
            $featuredNews = $this->newsService->getFeaturedNews(9, 10);
            $recentNews = $this->newsService->getRecent(10);
            $liveApplicationForm = $this->collegeService->getLiveApplicationFormData($article->college[0]['id']);
            $affiliatedCollege = $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id);
            $parentCollege  = $college->getParent()->one() ?? $college;
            // $reviews = $this->collegeService->getReviewByCategory($college->slug, [1, 2, 3, 4, 5, 7, 8,]);
            // $revRating = $this->collegeService->getReviewRating($college->old_id);
            // $revCategoryRating = $this->collegeService->getRevCategoryRating($college->old_id);
            // $revDistributionRating  = $this->collegeService->getRevDistributionRating($college->old_id);
            $productArticle = $college->article;
            $productNews = $college->news;
            $sponsorClientUrl = DataHelper::getRedirectionLink('college', $college->id);
            // $recentEntity = $college->id;
        } elseif ($article->exam) {
            $entityName = 'exams';
            $exam = Exam::find()->where(['=', 'id', $article->exam[0]['id']])->one();
            if (empty($exam)) {
                return [];
            }
            $checkFilterStatus = $this->collegeService->checkFilterPageStatus([], '', '', '', $article->exam[0]->slug);
            $entityId = $article->exam[0]['id'];
            $entitySlug = $article->exam[0]['slug'];
            $entityDisplayName = $article->exam[0]['name'];
            $entityId = $article->exam[0]['id'];
            $liveApplicationForm =  $this->examService->getLiveApplicationFormData($article->exam[0]);
            $pages =  $this->examService->getPages($article->exam[0]['id']);
            $menus = array_column(ArrayHelper::toArray($pages), 'slug');
            $popularExams = $this->examService->popularExams($article->exam[0]['id']);
            $primaryStream   = $article->exam[0]->primaryStream;
            $upcomingExamByStream = isset($primaryStream->id) ? $this->commonExamService->upcomingExams($primaryStream->id) : [];
            if (count($article->exam[0]->streams)) {
                $streams = $article->exam[0]->streams;
            }
            $streamId = $article->exam[0]->streams[0]->id ?? '';
            $popularExamsIds = array_column($popularExams, 'id');
            $notIn = array_merge($popularExamsIds, array_column($upcomingExamByStream, 'id'));
            $intrestedExams = $this->commonExamService->interestedArticleExams($streamId, $notIn, 4);
            $collegeAcceptingExams = $this->commonExamService->getCollegeAcceptingExams($article->exam[0]['id'], $article->exam[0]['slug']);
            $upcomingExams = $this->examService->upcomingExams(null, 7);
            $featuredNews = $this->newsService->getFeaturedNews(9, 10);
            $recentNews = $this->newsService->getRecent(10);
            $productArticle = $exam->article;
            $productNews = $exam->news;
            $sponsorClientUrl =  DataHelper::getRedirectionLink('exam', $exam->id);
            if (empty($article->articleSection)) {
                $topicArticleLink = $this->examService->getArticleTopicPageNormalArticle($article->exam[0]->slug, $article->id);
            }
            $downloadableResource = $this->examService->getDownloadableResource($exam->id, $exam->primary_stream_id, 'exam');
            // $recentEntity = $exam->id;
        } elseif ($article->course) {
            $course = Course::find()->where(['=', 'id', $article->course[0]['id']])->one();
            if (empty($course)) {
                return [];
            }
            $parentCourse = $article->course[0]->getParent()->one();
            $parentCourseName = $parentCourse->name ?? $article->course[0]['name'];
            $entityName = 'courses';
            $entityDisplayName = $article->course[0]['short_name'] ?? $article->course[0]['name'];
            $entityId = $article->course[0]['id'];
            $entityDisplayName = $article->course[0]['short_name'];
            $entitySlug = $article->course[0]['slug'];
            $menus =   $this->courseService->getMenu($article->course[0]['id']);
            $liveApplicationForm =  $this->courseService->getLiveApplication($article->course[0]['short_name'], 5);
            $courseSpecialization = $this->courseService->getParentSpecialization($course);
            $coursebyStream = $this->courseService->getStreamCourse($course);
            $streamName = $course->stream->name;
            $courseCollege =  $this->courseService->getTopColleges($course);
            $courseExams = $this->courseService->getExamByCourse($course);
            $Chartdata = $this->courseService->getChartData($course);
            $stateList = $this->courseService->getCourseByState($course);
            $featuredNews = $this->newsService->getFeaturedNews(9, 10);
            $recentNews = $this->newsService->getRecent(10);
            $showOtherCourseCategory = true;
            $productArticle = $course->article;
            $productNews = $course->news;
            // $recentEntity = $course->id;
        } elseif ($article->board) {
            $board = Board::find()->where(['=', 'id', $article->board[0]['id']])->one();
            if (empty($board)) {
                return [];
            }
            $entityId = $article->board[0]['id'];
            $boardExamList = $this->boardService->getExam($board->id);
            $content = $this->boardService->getContent($board->id, 'overview');
            $subject = $this->boardService->getBoardSubject($content->id);
            $sponsorClientUrl = DataHelper::getRedirectionLink('board', $board->id);
            $pageContent = $content;
            $content = BoardHelper::parseBoardContent($content->content);
            // $pages = $this->boardService->getPages($board->id);
            $boardarticles = $this->articleService->getByCategory(17);
            $boardExams  = $this->newsService->getCategoryNews(7);
            $stateColleges = $this->boardService->getCollegesByState($board->state_id);
            $featuredNews = $this->newsService->getFeaturedNews(9, 10);
            $recentNews = $this->newsService->getRecent(10);
            $menus =   $this->boardService->getMenu($board->id);
            $entityName = 'boards';
            $entityDisplayName = $article->board[0]['display_name'] ?? $article->board[0]['name'];
            $entityId = $article->board[0]['id'];
            $entitySlug = $article->board[0]['slug'];
            $productArticle = $board->article;
            $productNews = $board->news;
            $allBoardPage = $this->boardService->getAllBoardPage();
            // $recentEntity = $board->id;
        }
        if ($article['lang_code'] != 1) {
            $category_trns = $article->translatedCategory;
            if (!empty($category_trns)) {
                $category = $article->category;
                $category['name'] = $category_trns['name'];
                $category['display_name'] = $category_trns['display_name'];
                $category['description'] = $category_trns['description'];
                $category['h1'] = $category_trns['h1'];
                $category['meta_title'] = $category_trns['meta_title'];
                $category['meta_description'] = $category_trns['meta_description'];
            } else {
                $category = $article->category;
            }
        } else {
            $category = $article->category;
        }
            $getAuthorTrans = $this->authorTranslation($article->author_id, $article->lang_code);
        if (!empty($getAuthorTrans)) {
            $article_author = $getAuthorTrans->user_name;
            // $article_author = $article->author;
        } else {
            $article_author = $article->author;
        }
        if ($article->lang_code != 1) {
            $menus = [];
        }
            
            $articleStreamId = $primaryStream->id ?? $article->stream_id;
        if ($category->slug == 'boards' && $boardArticleUrl) {
            // dd($article->board);
            $recentArticles = $this->articleService->getCategoryBasedRecentArticles($article);
            $trendings = $this->articleService->getCategoryTrendingArticleWithAuthor($category->id);
            // dd($trendings);
        } else {
            $recentArticles = $this->articleService->getRecentArticles(Category::EXCLUDE_CATEGORY, 10, Article::ENTITY_ARTICLE, $article->stream_id);
            $trendings = $this->articleService->getTrendings(10, 'study-abroad', $article->stream_id);
        }

            $data =  [
                'commentModel' => new CommentForm(),
                'tags' => $tags,
                'article' => $article,
                'board' => !empty($board) ? $board : '',
                'author' => $article_author,
                'category' => $category,
                'relatedArticles' => $relatedArticle,
                'trendings' => $trendings,
                // 'trendings' => $this->articleService->getTrendings(10, 'study-abroad', $article->stream_id),
                // 'recentArticles' => $this->articleService->getRecentArticles(Category::EXCLUDE_CATEGORY, 10, Article::ENTITY_ARTICLE, $article->stream_id),
                'recentArticles' => $recentArticles,
                'faqs' => $this->faqService->getPageLevelFaqDetails(Article::ENTITY_ARTICLE, $article->id),
                'comments' => $this->articleService->getComments($article->id, Article::ENTITY_ARTICLE),
                'upcomingExams' => $upcomingExams ?? [],
                'popularExams' => $popularExams ?? [],
                'menus' => $menus ?? [],
                'upcomingExamByStream' => $upcomingExamByStream ?? [],
                'primaryStream' => $primaryStream ?? [],
                'streams' => $streams ?? [],
                'intrestedExams' => $intrestedExams ?? [],
                'collegeAcceptingExams' => $collegeAcceptingExams ?? [],
                'checkFilterStatus' => $checkFilterStatus ?? [],
                'liveApplicationForm' => $liveApplicationForm ?? [],
                'entityName' =>  $entityName ?? '',
                'entityId' => $entityId ?? '',
                'entitySlug' => $entitySlug ?? '',
                'entityId' => $entityId ?? '',
                'entityDisplayName' => $entityDisplayName ?? '',
                'courseSpecialization' => $courseSpecialization ?? [],
                'parentCourseName' => $parentCourseName ?? [],
                'coursebyStream' => $coursebyStream ?? [],
                'streamName' => $streamName ?? '',
                'courseCollege' => $courseCollege ?? [],
                'courseExams' => $courseExams ?? [],
                'Chartdata' => $Chartdata ?? [],
                'course' => $course ?? [],
                'stateList' => $stateList ?? [],
                'pages' => $pages ?? [],
                'exam' =>  $exam ?? [],
                'featuredNews' => $featuredNews ?? [],
                'recentNews' => $recentNews ?? [],
                'college' => $college ?? [],
                'nearByCollege' => $nearByCollege ?? [],
                'gallery' => $gallery ?? [],
                'collegeByDiscipline' => $collegeByDiscipline ?? [],
                'showOtherCourseCategory' => $showOtherCourseCategory ?? [],
                'affiliatedCollege' => $affiliatedCollege ?? [],
                'parentCollege' => $parentCollege ?? [],
                'productArticle' => $productArticle ?? [],
                'productNews' => $productNews ?? [],
                'sponsorClientUrl' => $sponsorClientUrl ?? '',
                'board' => $board ?? [],
                'boardExamList' => $boardExamList ?? [],
                'subject' => $subject ?? [],
                'pageContent' => $pageContent ?? [],
                'boardarticles' => $boardarticles ?? [],
                'boardExams'  => $boardExams ?? [],
                'stateColleges' => $stateColleges ?? [],
                'translation_data' => $active_trans_data,
                'recentStreamNews' => $this->newsService->getRecent(10, $articleStreamId),
                'dynamicCta' => !empty($dynamicCta) ?  $dynamicCta : (UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_ARTICLE, $article->id, $article->title, $article->slug) ?? []),
                'articleSection' => $this->articleService->getArticlePracticeDetail($article) ?? [],
                'topicArticleLink'=>$topicArticleLink ?? [],
                'allBoardPage' => $allBoardPage ?? [],
                'downloadableResource' => $downloadableResource ?? []
                // 'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Article::ENTITY_ARTICLE, $recentEntity), $entityDisplayName]
            ];
        // }, 60 * 60 * 3, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
            return $this->render('detail', $data);
    }

    public function authorTranslation($author_id, $lang_code)
    {
        $author_translation = UserTranslation::find()->select(['user_name'])
            ->where(['tag_user_id' => $author_id])
            ->andWhere(['lang_code' => $lang_code])
            ->one();
        return $author_translation;
    }

    public function showCategory($category)
    {
        $urlExpload = explode('/', \Yii::$app->request->getUrl());
        if (isset($urlExpload[1]) && $urlExpload[1]=='amp') {
            throw new NotFoundHttpException();
        }
        $lang_code = DataHelper::getLangId();
        $articles = new ActiveDataProvider([
            'query' => Article::find()->select([
                'id', 'h1', 'entity', 'cover_image', 'slug', 'meta_description', 'country_slug', 'meta_title', 'author_id', 'updated_at', 'is_popular', 'lang_code'
            ])->where(['category_id' => $category->id])
                ->andWhere(['lang_code' => $lang_code])
                ->with('author')
                ->orderBy(['is_popular' => SORT_DESC, 'updated_at' => SORT_DESC])
                ->active(),
            'pagination' => ['pageSize' => 20]
        ]);
        // redirect to category landing page if request page is greater than the pagination page
        $articles->prepare();
        if ((int) Yii::$app->request->get('page') > $articles->pagination->pageCount) {
            $this->redirect(Url::toArticleDetail($category->slug), 302);
            Yii::$app->end();
        }

        $data = [
            'category' => $category,
            'articles' => $articles,
            'trendings' => $this->articleService->getTrendingArticleWithAuthor(),
            'recentArticles' => $this->articleService->getAll(),
        ];
        return $this->render('category', $data);
    }

    public function actionTag($slug)
    {
        $tag = $this->articleService->getTagBySlug($slug);
        if (!$tag) {
            throw new NotFoundHttpException();
        }

        $articles = $this->articleService->byTagId($tag, null, 20);

        // redirect to category landing page if request page is greater than the pagination page
        $articles->prepare();
        if ((int) Yii::$app->request->get('page') > $articles->pagination->pageCount) {
            $this->redirect(Url::toTag($tag->slug), 302);
            Yii::$app->end();
        }

        $data = [
            'tag' => $tag,
            'articles' => $articles,
            'trendings' => $this->articleService->getTrendingArticleWithAuthor(),
            'recentArticles' => $this->articleService->getAll(5, [], Category::EXCLUDE_CATEGORY),
        ];

        return $this->render('tag', $data);
    }

    public function actionStudyAbroadIndex($country)
    {
        $studyAbroadArticles = new ActiveDataProvider([
            'query' => Article::find()->select([
                'id', 'h1', 'entity', 'country_slug', 'cover_image', 'slug', 'meta_description', 'meta_title', 'author_id', 'updated_at'
            ])->with('author')
                ->where(['country_slug' => $country])
                ->andWhere(['status' => Article::STATUS_ACTIVE])
                ->orderBy(['updated_at' => SORT_DESC]),
            'pagination' => ['pageSize' => 20]
        ]);

        $studyAbroadArticles->prepare();

        if ($studyAbroadArticles->totalCount == 0) {
            throw new NotFoundHttpException();
        }

        if ((int) Yii::$app->request->get('page') > $studyAbroadArticles->pagination->pageCount) {
            $this->redirect(Url::toStudyAbroadArticles($country), 302);
            Yii::$app->end();
        }

        $data = [
            'articles' => $studyAbroadArticles,
            'trendings' => $this->studyAbroadService->getTrendingArticle($country, 5),
            'latestArticle' => $this->studyAbroadService->getLatestArticle($country, 5),
            'country' => $this->studyAbroadService->getCountry($country),
        ];

        return $this->render('study-abroad-index', $data);
    }

    public function actionStudyAbroadDetail($country, $slug)
    {
        $relatedArticle = [];

        $detail = $this->studyAbroadService->getArticleCountryDetail($country, $slug);

        if (empty($detail)) {
            throw new NotFoundHttpException();
        }

        $tags = $detail->tags;

        if (isset($tags[0])) {
            $relatedArticle = $this->articleService->byTagId($tags[0], 4);
        }

        $data = [
            'detail' => $detail,
            'author' => $detail->author,
            'tags' => $tags,
            'relatedArticles' => $relatedArticle,
            'faqs' => $this->faqService->getPageLevelFaqDetails(Article::ENTITY_ARTICLE, $detail->id),
            'trendings' => $this->studyAbroadService->getTrendingArticle($detail->country_slug, 5),
            'latestArticle' => $this->studyAbroadService->getLatestArticle($detail->country_slug, 5),
            'commentModel' => new CommentForm(),
            'comments' => $this->articleService->getComments($detail->id, Article::ENTITY_STUDY_ABROAD),
            'country' => $this->studyAbroadService->getCountry($detail->country_slug),
        ];

        return $this->render('study-abroad-detail', $data);
    }

    public function actionSubpage($slug)
    {
        $article = $this->articleService->getDetail($slug);
        if (!$article) {
            throw new NotFoundHttpException();
        }

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'article-detail';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        // Yii::$app->cache->delete($key);
        $data = Yii::$app->cache->getOrSet($key, function () use ($article) {
            if ($data = $this->articleService->getAdTargetData($article)) {
                Ad::setTargetArticleNews($data);
            }
            $tags = $article->tags;
            $recentEntity = $article['id'];
            $entityName = '';
            $entityDisplayName = '';
            if (!empty($article->audio)) {
                $getS3Audio = DataHelper::s3Path($article->audio, 'article_audio', true);
                $article->audio = $getS3Audio;
            }

            $relatedArticle = $this->articleService->getByCategory($article->category->id) ?? [];
            $active_trans_data = [];
            foreach ($article->activeTranslation as $trans_data) {
                $active_trans['cu_lang'] = DataHelper::getLangCode($article['lang_code']);
                $active_trans['slug'] = $trans_data['slug'];
                $active_trans['lang'] = DataHelper::getLangCode($trans_data['lang_code']);
                $active_trans_data[] = $active_trans;
            }
            if ($article->college) {
                $entityName = 'colleges';
                $college = College::find()->where(['=', 'id', $article->college[0]['id']])->one();
                if (empty($college)) {
                    return [];
                }
                $entityId = $article->college[0]['id'];
                $nearByCollege = $this->collegeService->getCollegesByCity($college->city_id, 4, $college->id);
                $gallery  = $this->collegeService->getGallery($college, 4);
                $collegeByDiscipline  = $this->collegeService->getCollegeByDiscipline($college->id, $college->city->id);
                $entityDisplayName = $article->college[0]['name'];
                $entityId = $article->college[0]['id'];
                $menus = $this->collegeService->getMenu($article->college[0]);
                $entitySlug = $article->college[0]['slug'];
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $liveApplicationForm = $this->collegeService->getLiveApplicationFormData($article->college[0]['id']);
                $affiliatedCollege = $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id);
                $parentCollege  = $college->getParent()->one() ?? $college;
                // $reviews = $this->collegeService->getReviewByCategory($college->slug, [1, 2, 3, 4, 5, 7, 8,]);
                // $revRating = $this->collegeService->getReviewRating($college->old_id);
                // $revCategoryRating = $this->collegeService->getRevCategoryRating($college->old_id);
                // $revDistributionRating  = $this->collegeService->getRevDistributionRating($college->old_id);
                $productArticle = $college->article;
                $productNews = $college->news;
                $sponsorClientUrl = DataHelper::getRedirectionLink('college', $college->id);
                // $recentEntity = $college->id;
            } elseif ($article->exam) {
                $entityName = 'exams';
                $exam = Exam::find()->where(['=', 'id', $article->exam[0]['id']])->one();
                if (empty($exam)) {
                    return [];
                }
                $checkFilterStatus = $this->collegeService->checkFilterPageStatus([], '', '', '', $article->exam[0]->slug);
                $entityId = $article->exam[0]['id'];
                $entitySlug = $article->exam[0]['slug'];
                $entityDisplayName = $article->exam[0]['name'];
                $entityId = $article->exam[0]['id'];
                $liveApplicationForm =  $this->examService->getLiveApplicationFormData($article->exam[0]);
                $pages =  $this->examService->getPages($article->exam[0]['id']);
                $menus = array_column(ArrayHelper::toArray($pages), 'slug');
                $popularExams = $this->examService->popularExams($article->exam[0]['id']);
                $primaryStream   = $article->exam[0]->primaryStream;
                $upcomingExamByStream = isset($primaryStream->id) ? $this->commonExamService->upcomingExams($primaryStream->id) : [];
                if (count($article->exam[0]->streams)) {
                    $streams = $article->exam[0]->streams;
                }
                $streamId = $article->exam[0]->streams[0]->id ?? '';
                $popularExamsIds = array_column($popularExams, 'id');
                $notIn = array_merge($popularExamsIds, array_column($upcomingExamByStream, 'id'));
                $intrestedExams = $this->commonExamService->interestedArticleExams($streamId, $notIn, 4);
                $collegeAcceptingExams = $this->commonExamService->getCollegeAcceptingExams($article->exam[0]['id'], $article->exam[0]['slug']);
                $upcomingExams = $this->examService->upcomingExams(null, 7);
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $productArticle = $exam->article;
                $productNews = $exam->news;
                $sponsorClientUrl =  DataHelper::getRedirectionLink('exam', $exam->id);
                $downloadableResource = $this->examService->getDownloadableResource($exam->id, $exam->primary_stream_id, 'exam');
                // $recentEntity = $exam->id;
            } elseif ($article->course) {
                $course = Course::find()->where(['=', 'id', $article->course[0]['id']])->one();
                if (empty($course)) {
                    return [];
                }
                $parentCourse = $article->course[0]->getParent()->one();
                $parentCourseName = $parentCourse->name ?? $article->course[0]['name'];
                $entityName = 'courses';
                $entityDisplayName = $article->course[0]['short_name'] ?? $article->course[0]['name'];
                $entityId = $article->course[0]['id'];
                $entityDisplayName = $article->course[0]['short_name'];
                $entitySlug = $article->course[0]['slug'];
                $menus =   $this->courseService->getMenu($article->course[0]['id']);
                $liveApplicationForm =  $this->courseService->getLiveApplication($article->course[0]['short_name'], 5);
                $courseSpecialization = $this->courseService->getParentSpecialization($course);
                $coursebyStream = $this->courseService->getStreamCourse($course);
                $streamName = $course->stream->name;
                $courseCollege =  $this->courseService->getTopColleges($course);
                $courseExams = $this->courseService->getExamByCourse($course);
                $Chartdata = $this->courseService->getChartData($course);
                $stateList = $this->courseService->getCourseByState($course);
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $showOtherCourseCategory = true;
                $productArticle = $course->article;
                $productNews = $course->news;
                // $recentEntity = $course->id;
            } elseif ($article->board) {
                $board = Board::find()->where(['=', 'id', $article->board[0]['id']])->one();
                if (empty($board)) {
                    return [];
                }
                $entityId = $article->board[0]['id'];
                $boardExamList = $this->boardService->getExam($board->id);
                $content = $this->boardService->getContent($board->id, 'overview');
                $subject = $this->boardService->getBoardSubject($content->id);
                $sponsorClientUrl = DataHelper::getRedirectionLink('board', $board->id);
                $pageContent = $content;
                $content = BoardHelper::parseBoardContent($content->content);
                // $pages = $this->boardService->getPages($board->id);
                $boardarticles = $this->articleService->getByCategory(17);
                $boardExams  = $this->newsService->getCategoryNews(7);
                $stateColleges = $this->boardService->getCollegesByState($board->state_id);
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $menus =   $this->boardService->getMenu($board->id);
                $entityName = 'boards';
                $entityDisplayName = $article->board[0]['display_name'] ?? $article->board[0]['name'];
                $entityId = $article->board[0]['id'];
                $entitySlug = $article->board[0]['slug'];
                $productArticle = $board->article;
                $productNews = $board->news;
                // $recentEntity = $board->id;
            }
            if ($article['lang_code'] != 1) {
                $category_trns = $article->translatedCategory;
                if (!empty($category_trns)) {
                    $category = $article->category;
                    $category['name'] = $category_trns['name'];
                    $category['display_name'] = $category_trns['display_name'];
                    $category['description'] = $category_trns['description'];
                    $category['h1'] = $category_trns['h1'];
                    $category['meta_title'] = $category_trns['meta_title'];
                    $category['meta_description'] = $category_trns['meta_description'];
                } else {
                    $category = $article->category;
                }
            } else {
                $category = $article->category;
            }
            $getAuthorTrans = $this->authorTranslation($article->author_id, $article->lang_code);
            if (!empty($getAuthorTrans)) {
                $article_author = $getAuthorTrans->user_name;
                // $article_author = $article->author;
            } else {
                $article_author = $article->author;
            }
            if ($article->lang_code != 1) {
                $menus = [];
            }
            
            $articleStreamId = $primaryStream->id ?? $article->stream_id;

            return [
                'commentModel' => new CommentForm(),
                'tags' => $tags,
                'article' => $article,
                'board' => !empty($board) ? $board : '',
                'author' => $article_author,
                'category' => $category,
                'relatedArticles' => $relatedArticle,
                'trendings' => $this->articleService->getTrendings(10, 'study-abroad', $article->stream_id),
                'recentArticles' => $this->articleService->getRecentArticles(Category::EXCLUDE_CATEGORY, 10, Article::ENTITY_ARTICLE, $article->stream_id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(Article::ENTITY_ARTICLE, $article->id),
                'comments' => $this->articleService->getComments($article->id, Article::ENTITY_ARTICLE),
                'upcomingExams' => $upcomingExams ?? [],
                'popularExams' => $popularExams ?? [],
                'menus' => $menus ?? [],
                'upcomingExamByStream' => $upcomingExamByStream ?? [],
                'primaryStream' => $primaryStream ?? [],
                'streams' => $streams ?? [],
                'intrestedExams' => $intrestedExams ?? [],
                'collegeAcceptingExams' => $collegeAcceptingExams ?? [],
                'checkFilterStatus' => $checkFilterStatus ?? [],
                'liveApplicationForm' => $liveApplicationForm ?? [],
                'entityName' =>  $entityName ?? '',
                'entityId' => $entityId ?? '',
                'entitySlug' => $entitySlug ?? '',
                'entityId' => $entityId ?? '',
                'entityDisplayName' => $entityDisplayName ?? '',
                'courseSpecialization' => $courseSpecialization ?? [],
                'parentCourseName' => $parentCourseName ?? [],
                'coursebyStream' => $coursebyStream ?? [],
                'streamName' => $streamName ?? '',
                'courseCollege' => $courseCollege ?? [],
                'courseExams' => $courseExams ?? [],
                'Chartdata' => $Chartdata ?? [],
                'course' => $course ?? [],
                'stateList' => $stateList ?? [],
                'pages' => $pages ?? [],
                'exam' =>  $exam ?? [],
                'featuredNews' => $featuredNews ?? [],
                'recentNews' => $recentNews ?? [],
                'college' => $college ?? [],
                'nearByCollege' => $nearByCollege ?? [],
                'gallery' => $gallery ?? [],
                'collegeByDiscipline' => $collegeByDiscipline ?? [],
                'showOtherCourseCategory' => $showOtherCourseCategory ?? [],
                'affiliatedCollege' => $affiliatedCollege ?? [],
                'parentCollege' => $parentCollege ?? [],
                // 'reviews' => $reviews,
                // 'revRating' => $revRating,
                // 'revCategoryRating' => $revCategoryRating,
                // 'revDistributionRating'  => $revDistributionRating,
                'productArticle' => $productArticle ?? [],
                'productNews' => $productNews ?? [],
                'sponsorClientUrl' => $sponsorClientUrl ?? '',
                'board' => $board ?? [],
                'boardExamList' => $boardExamList ?? [],
                'subject' => $subject ?? [],
                'pageContent' => $pageContent ?? [],
                'boardarticles' => $boardarticles ?? [],
                'boardExams'  => $boardExams ?? [],
                'stateColleges' => $stateColleges ?? [],
                'translation_data' => $active_trans_data,
                'recentStreamNews' => $this->newsService->getRecent(10, $articleStreamId),
                'dynamicCta' => !empty($dynamicCta) ?  $dynamicCta : (UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_ARTICLE, $article->id, $article->title, $article->slug) ?? []),
                'downloadableResource' => $downloadableResource ?? []
                // 'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Article::ENTITY_ARTICLE, $recentEntity), $entityDisplayName]
            ];
        }, 60 * 60 * 3, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
        return $this->render('article-practice-set', $data);
    }

    public function showSection($article, $section_id)
    {
        // $urlExpload = explode('/', \Yii::$app->request->getUrl());
        // if (isset($urlExpload[1]) && $urlExpload[1]=='amp' && !in_array($article->slug, ['food-technology-courses', 'good-score-in-clat-2025', 'top-law-colleges-accepting-ailet-scores-and-their-cut-offs'])) {
        //     throw new NotFoundHttpException();
        // }

        if (!$article) {
            throw new NotFoundHttpException();
        }

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'article-detail';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
         //Yii::$app->cache->delete($key);
       // $data = Yii::$app->cache->getOrSet($key, function () use ($article, $section_id) {
        if ($data = $this->articleService->getAdTargetData($article)) {
            Ad::setTargetArticleNews($data);
        }
            $tags = $article->tags;
            $recentEntity = $article['id'];
            $entityName = '';
            $entityDisplayName = '';
        if (!empty($article->audio)) {
            $getS3Audio = DataHelper::s3Path($article->audio, 'article_audio', true);
            $article->audio = $getS3Audio;
        }
            $relatedArticle = $this->articleService->getByCategory($article->category->id) ?? [];
            $active_trans_data = [];
        foreach ($article->activeTranslation as $trans_data) {
            $active_trans['cu_lang'] = DataHelper::getLangCode($article['lang_code']);
            $active_trans['slug'] = $trans_data['slug'];
            $active_trans['lang'] = DataHelper::getLangCode($trans_data['lang_code']);
            $active_trans_data[] = $active_trans;
        }
        if ($article->college) {
            $entityName = 'colleges';
            $college = College::find()->where(['=', 'id', $article->college[0]['id']])->one();
            if (empty($college)) {
                return [];
            }
            $entityId = $article->college[0]['id'];
            $nearByCollege = $this->collegeService->getCollegesByCity($college->city_id, 4, $college->id);
            $gallery  = $this->collegeService->getGallery($college, 4);
            $collegeByDiscipline  = $this->collegeService->getCollegeByDiscipline($college->id, $college->city->id);
            $entityDisplayName = $article->college[0]['name'];
            $entityId = $article->college[0]['id'];
            $menus = $this->collegeService->getMenu($article->college[0]);
            $entitySlug = $article->college[0]['slug'];
            $featuredNews = $this->newsService->getFeaturedNews(9, 10);
            $recentNews = $this->newsService->getRecent(10);
            $liveApplicationForm = $this->collegeService->getLiveApplicationFormData($article->college[0]['id']);
            $affiliatedCollege = $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id);
            $parentCollege  = $college->getParent()->one() ?? $college;
            // $reviews = $this->collegeService->getReviewByCategory($college->slug, [1, 2, 3, 4, 5, 7, 8,]);
            // $revRating = $this->collegeService->getReviewRating($college->old_id);
            // $revCategoryRating = $this->collegeService->getRevCategoryRating($college->old_id);
            // $revDistributionRating  = $this->collegeService->getRevDistributionRating($college->old_id);
            $productArticle = $college->article;
            $productNews = $college->news;
            $sponsorClientUrl = DataHelper::getRedirectionLink('college', $college->id);
            // $recentEntity = $college->id;
        } elseif ($article->exam) {
            $entityName = 'exams';
            $exam = Exam::find()->where(['=', 'id', $article->exam[0]['id']])->one();
            if (empty($exam)) {
                return [];
            }
            $checkFilterStatus = $this->collegeService->checkFilterPageStatus([], '', '', '', $article->exam[0]->slug);
            $entityId = $article->exam[0]['id'];
            $entitySlug = $article->exam[0]['slug'];
            $entityDisplayName = $article->exam[0]['name'];
            $entityId = $article->exam[0]['id'];
            $liveApplicationForm =  $this->examService->getLiveApplicationFormData($article->exam[0]);
            $pages =  $this->examService->getPages($article->exam[0]['id']);
            $menus = array_column(ArrayHelper::toArray($pages), 'slug');
            $popularExams = $this->examService->popularExams($article->exam[0]['id']);
            $primaryStream   = $article->exam[0]->primaryStream;
            $upcomingExamByStream = isset($primaryStream->id) ? $this->commonExamService->upcomingExams($primaryStream->id) : [];
            if (count($article->exam[0]->streams)) {
                $streams = $article->exam[0]->streams;
            }
            $streamId = $article->exam[0]->streams[0]->id ?? '';
            $popularExamsIds = array_column($popularExams, 'id');
            $notIn = array_merge($popularExamsIds, array_column($upcomingExamByStream, 'id'));
            $intrestedExams = $this->commonExamService->interestedArticleExams($streamId, $notIn, 4);
            $collegeAcceptingExams = $this->commonExamService->getCollegeAcceptingExams($article->exam[0]['id'], $article->exam[0]['slug']);
            $upcomingExams = $this->examService->upcomingExams(null, 7);
            $featuredNews = $this->newsService->getFeaturedNews(9, 10);
            $recentNews = $this->newsService->getRecent(10);
            $productArticle = $exam->article;
            $productNews = $exam->news;
            $sponsorClientUrl =  DataHelper::getRedirectionLink('exam', $exam->id);
            // $recentEntity = $exam->id;
        } elseif ($article->course) {
            $course = Course::find()->where(['=', 'id', $article->course[0]['id']])->one();
            if (empty($course)) {
                return [];
            }
            $parentCourse = $article->course[0]->getParent()->one();
            $parentCourseName = $parentCourse->name ?? $article->course[0]['name'];
            $entityName = 'courses';
            $entityDisplayName = $article->course[0]['short_name'] ?? $article->course[0]['name'];
            $entityId = $article->course[0]['id'];
            $entityDisplayName = $article->course[0]['short_name'];
            $entitySlug = $article->course[0]['slug'];
            $menus =   $this->courseService->getMenu($article->course[0]['id']);
            $liveApplicationForm =  $this->courseService->getLiveApplication($article->course[0]['short_name'], 5);
            $courseSpecialization = $this->courseService->getParentSpecialization($course);
            $coursebyStream = $this->courseService->getStreamCourse($course);
            $streamName = $course->stream->name;
            $courseCollege =  $this->courseService->getTopColleges($course);
            $courseExams = $this->courseService->getExamByCourse($course);
            $Chartdata = $this->courseService->getChartData($course);
            $stateList = $this->courseService->getCourseByState($course);
            $featuredNews = $this->newsService->getFeaturedNews(9, 10);
            $recentNews = $this->newsService->getRecent(10);
            $showOtherCourseCategory = true;
            $productArticle = $course->article;
            $productNews = $course->news;
            // $recentEntity = $course->id;
        } elseif ($article->board) {
            $board = Board::find()->where(['=', 'id', $article->board[0]['id']])->one();
            if (empty($board)) {
                return [];
            }
            $entityId = $article->board[0]['id'];
            $boardExamList = $this->boardService->getExam($board->id);
            $content = $this->boardService->getContent($board->id, 'overview');
            $subject = $this->boardService->getBoardSubject($content->id);
            $sponsorClientUrl = DataHelper::getRedirectionLink('board', $board->id);
            $pageContent = $content;
            $content = BoardHelper::parseBoardContent($content->content);
            // $pages = $this->boardService->getPages($board->id);
            $boardarticles = $this->articleService->getByCategory(17);
            $boardExams  = $this->newsService->getCategoryNews(7);
            $stateColleges = $this->boardService->getCollegesByState($board->state_id);
            $featuredNews = $this->newsService->getFeaturedNews(9, 10);
            $recentNews = $this->newsService->getRecent(10);
            $menus =   $this->boardService->getMenu($board->id);
            $entityName = 'boards';
            $entityDisplayName = $article->board[0]['display_name'] ?? $article->board[0]['name'];
            $entityId = $article->board[0]['id'];
            $entitySlug = $article->board[0]['slug'];
            $productArticle = $board->article;
            $productNews = $board->news;
            // $recentEntity = $board->id;
        }
        if ($article['lang_code'] != 1) {
            $category_trns = $article->translatedCategory;
            if (!empty($category_trns)) {
                $category = $article->category;
                $category['name'] = $category_trns['name'];
                $category['display_name'] = $category_trns['display_name'];
                $category['description'] = $category_trns['description'];
                $category['h1'] = $category_trns['h1'];
                $category['meta_title'] = $category_trns['meta_title'];
                $category['meta_description'] = $category_trns['meta_description'];
            } else {
                $category = $article->category;
            }
        } else {
            $category = $article->category;
        }
            $getAuthorTrans = $this->authorTranslation($article->author_id, $article->lang_code);
        if (!empty($getAuthorTrans)) {
            $article_author = $getAuthorTrans->user_name;
            // $article_author = $article->author;
        } else {
            $article_author = $article->author;
        }
        if ($article->lang_code != 1) {
            $menus = [];
        }
            
            $articleStreamId = $primaryStream->id ?? $article->stream_id;
            $articleSectionSubTopic =  $this->articleService->getArticlePracticeDetailSubTopic($article, $section_id) ?? [];
            $articleSectionActiveSection =  $this->articleService->getArticlePracticeDetailActiveSection($article, $section_id) ?? [];
            $articleSectionData =  ArticleSubpageSection::find()->where(['id'=>$section_id])->one();
            $data = [
                'commentModel' => new CommentForm(),
                'tags' => $tags,
                'article' => $article,
                'board' => !empty($board) ? $board : '',
                'author' => $article_author,
                'category' => $category,
                'relatedArticles' => $relatedArticle,
                'trendings' => $this->articleService->getTrendings(10, 'study-abroad', $article->stream_id),
                'recentArticles' => $this->articleService->getRecentArticles(Category::EXCLUDE_CATEGORY, 10, Article::ENTITY_ARTICLE, $article->stream_id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(Article::ENTITY_ARTICLE, $article->id),
                'comments' => $this->articleService->getComments($article->id, Article::ENTITY_ARTICLE),
                'upcomingExams' => $upcomingExams ?? [],
                'popularExams' => $popularExams ?? [],
                'menus' => $menus ?? [],
                'upcomingExamByStream' => $upcomingExamByStream ?? [],
                'primaryStream' => $primaryStream ?? [],
                'streams' => $streams ?? [],
                'intrestedExams' => $intrestedExams ?? [],
                'collegeAcceptingExams' => $collegeAcceptingExams ?? [],
                'checkFilterStatus' => $checkFilterStatus ?? [],
                'liveApplicationForm' => $liveApplicationForm ?? [],
                'entityName' =>  $entityName ?? '',
                'entityId' => $entityId ?? '',
                'entitySlug' => $entitySlug ?? '',
                'entityId' => $entityId ?? '',
                'entityDisplayName' => $entityDisplayName ?? '',
                'courseSpecialization' => $courseSpecialization ?? [],
                'parentCourseName' => $parentCourseName ?? [],
                'coursebyStream' => $coursebyStream ?? [],
                'streamName' => $streamName ?? '',
                'courseCollege' => $courseCollege ?? [],
                'courseExams' => $courseExams ?? [],
                'Chartdata' => $Chartdata ?? [],
                'course' => $course ?? [],
                'stateList' => $stateList ?? [],
                'pages' => $pages ?? [],
                'exam' =>  $exam ?? [],
                'featuredNews' => $featuredNews ?? [],
                'recentNews' => $recentNews ?? [],
                'college' => $college ?? [],
                'nearByCollege' => $nearByCollege ?? [],
                'gallery' => $gallery ?? [],
                'collegeByDiscipline' => $collegeByDiscipline ?? [],
                'showOtherCourseCategory' => $showOtherCourseCategory ?? [],
                'affiliatedCollege' => $affiliatedCollege ?? [],
                'parentCollege' => $parentCollege ?? [],
                // 'reviews' => $reviews,
                // 'revRating' => $revRating,
                // 'revCategoryRating' => $revCategoryRating,
                // 'revDistributionRating'  => $revDistributionRating,
                'productArticle' => $productArticle ?? [],
                'productNews' => $productNews ?? [],
                'sponsorClientUrl' => $sponsorClientUrl ?? '',
                'board' => $board ?? [],
                'boardExamList' => $boardExamList ?? [],
                'subject' => $subject ?? [],
                'pageContent' => $pageContent ?? [],
                'boardarticles' => $boardarticles ?? [],
                'boardExams'  => $boardExams ?? [],
                'stateColleges' => $stateColleges ?? [],
                'translation_data' => $active_trans_data,
                'recentStreamNews' => $this->newsService->getRecent(10, $articleStreamId),
                'dynamicCta' => !empty($dynamicCta) ?  $dynamicCta : (UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_ARTICLE, $article->id, $article->title, $article->slug) ?? []),
                'articleSection' => $this->articleService->getArticlePracticeDetail($article) ?? [],
                'articleSectionSubTopic'=>$articleSectionSubTopic,
                'articleSectionData'=> $articleSectionData,
                'articleSectionActiveSection'=>$articleSectionActiveSection,
                'articleSectionRestSection'=>$this->articleService->getArticlePracticeDetailRestSection($article, $section_id) ?? []
                // 'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Article::ENTITY_ARTICLE, $recentEntity), $entityDisplayName]
            ];
       // }, 60 * 60 * 3, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
            return $this->render('detail-section', $data);
    }
    public function showSuSection($article, $article_subpage_section_id, $article_subpage_section_topic_id, $articleSubSectionTopic_id)
    {
          
        if (!$article) {
            throw new NotFoundHttpException();
        }

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'article-detail';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);

        $data = Yii::$app->cache->getOrSet($key, function () use ($article, $article_subpage_section_id, $article_subpage_section_topic_id, $articleSubSectionTopic_id) {
            if ($data = $this->articleService->getAdTargetData($article)) {
                Ad::setTargetArticleNews($data);
            }
            $tags = $article->tags;
            $recentEntity = $article['id'];
            $entityName = '';
            $entityDisplayName = '';
            if (!empty($article->audio)) {
                $getS3Audio = DataHelper::s3Path($article->audio, 'article_audio', true);
                $article->audio = $getS3Audio;
            }
            $relatedArticle = $this->articleService->getByCategory($article->category->id) ?? [];
            $active_trans_data = [];
            foreach ($article->activeTranslation as $trans_data) {
                $active_trans['cu_lang'] = DataHelper::getLangCode($article['lang_code']);
                $active_trans['slug'] = $trans_data['slug'];
                $active_trans['lang'] = DataHelper::getLangCode($trans_data['lang_code']);
                $active_trans_data[] = $active_trans;
            }
            if ($article->college) {
                $entityName = 'colleges';
                $college = College::find()->where(['=', 'id', $article->college[0]['id']])->one();
                if (empty($college)) {
                    return [];
                }
                $entityId = $article->college[0]['id'];
                $nearByCollege = $this->collegeService->getCollegesByCity($college->city_id, 4, $college->id);
                $gallery  = $this->collegeService->getGallery($college, 4);
                $collegeByDiscipline  = $this->collegeService->getCollegeByDiscipline($college->id, $college->city->id);
                $entityDisplayName = $article->college[0]['name'];
                $entityId = $article->college[0]['id'];
                $menus = $this->collegeService->getMenu($article->college[0]);
                $entitySlug = $article->college[0]['slug'];
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $liveApplicationForm = $this->collegeService->getLiveApplicationFormData($article->college[0]['id']);
                $affiliatedCollege = $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id);
                $parentCollege  = $college->getParent()->one() ?? $college;
                $productArticle = $college->article;
                $productNews = $college->news;
                $sponsorClientUrl = DataHelper::getRedirectionLink('college', $college->id);
            } elseif ($article->exam) {
                $entityName = 'exams';
                $exam = Exam::find()->where(['=', 'id', $article->exam[0]['id']])->one();
                if (empty($exam)) {
                    return [];
                }
                $checkFilterStatus = $this->collegeService->checkFilterPageStatus([], '', '', '', $article->exam[0]->slug);
                $entityId = $article->exam[0]['id'];
                $entitySlug = $article->exam[0]['slug'];
                $entityDisplayName = $article->exam[0]['name'];
                $entityId = $article->exam[0]['id'];
                $liveApplicationForm =  $this->examService->getLiveApplicationFormData($article->exam[0]);
                $pages =  $this->examService->getPages($article->exam[0]['id']);
                $menus = array_column(ArrayHelper::toArray($pages), 'slug');
                $popularExams = $this->examService->popularExams($article->exam[0]['id']);
                $primaryStream   = $article->exam[0]->primaryStream;
                $upcomingExamByStream = isset($primaryStream->id) ? $this->commonExamService->upcomingExams($primaryStream->id) : [];
                if (count($article->exam[0]->streams)) {
                    $streams = $article->exam[0]->streams;
                }
                $streamId = $article->exam[0]->streams[0]->id ?? '';
                $popularExamsIds = array_column($popularExams, 'id');
                $notIn = array_merge($popularExamsIds, array_column($upcomingExamByStream, 'id'));
                $intrestedExams = $this->commonExamService->interestedArticleExams($streamId, $notIn, 4);
                $collegeAcceptingExams = $this->commonExamService->getCollegeAcceptingExams($article->exam[0]['id'], $article->exam[0]['slug']);
                $upcomingExams = $this->examService->upcomingExams(null, 7);
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $productArticle = $exam->article;
                $productNews = $exam->news;
                $sponsorClientUrl =  DataHelper::getRedirectionLink('exam', $exam->id);
                // $recentEntity = $exam->id;
            } elseif ($article->course) {
                $course = Course::find()->where(['=', 'id', $article->course[0]['id']])->one();
                if (empty($course)) {
                    return [];
                }
                $parentCourse = $article->course[0]->getParent()->one();
                $parentCourseName = $parentCourse->name ?? $article->course[0]['name'];
                $entityName = 'courses';
                $entityDisplayName = $article->course[0]['short_name'] ?? $article->course[0]['name'];
                $entityId = $article->course[0]['id'];
                $entityDisplayName = $article->course[0]['short_name'];
                $entitySlug = $article->course[0]['slug'];
                $menus =   $this->courseService->getMenu($article->course[0]['id']);
                $liveApplicationForm =  $this->courseService->getLiveApplication($article->course[0]['short_name'], 5);
                $courseSpecialization = $this->courseService->getParentSpecialization($course);
                $coursebyStream = $this->courseService->getStreamCourse($course);
                $streamName = $course->stream->name;
                $courseCollege =  $this->courseService->getTopColleges($course);
                $courseExams = $this->courseService->getExamByCourse($course);
                $Chartdata = $this->courseService->getChartData($course);
                $stateList = $this->courseService->getCourseByState($course);
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $showOtherCourseCategory = true;
                $productArticle = $course->article;
                $productNews = $course->news;
                // $recentEntity = $course->id;
            } elseif ($article->board) {
                $board = Board::find()->where(['=', 'id', $article->board[0]['id']])->one();
                if (empty($board)) {
                    return [];
                }
                $entityId = $article->board[0]['id'];
                $boardExamList = $this->boardService->getExam($board->id);
                $content = $this->boardService->getContent($board->id, 'overview');
                $subject = $this->boardService->getBoardSubject($content->id);
                $sponsorClientUrl = DataHelper::getRedirectionLink('board', $board->id);
                $pageContent = $content;
                $content = BoardHelper::parseBoardContent($content->content);
                // $pages = $this->boardService->getPages($board->id);
                $boardarticles = $this->articleService->getByCategory(17);
                $boardExams  = $this->newsService->getCategoryNews(7);
                $stateColleges = $this->boardService->getCollegesByState($board->state_id);
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $menus =   $this->boardService->getMenu($board->id);
                $entityName = 'boards';
                $entityDisplayName = $article->board[0]['display_name'] ?? $article->board[0]['name'];
                $entityId = $article->board[0]['id'];
                $entitySlug = $article->board[0]['slug'];
                $productArticle = $board->article;
                $productNews = $board->news;
                // $recentEntity = $board->id;
            }
            if ($article['lang_code'] != 1) {
                $category_trns = $article->translatedCategory;
                if (!empty($category_trns)) {
                    $category = $article->category;
                    $category['name'] = $category_trns['name'];
                    $category['display_name'] = $category_trns['display_name'];
                    $category['description'] = $category_trns['description'];
                    $category['h1'] = $category_trns['h1'];
                    $category['meta_title'] = $category_trns['meta_title'];
                    $category['meta_description'] = $category_trns['meta_description'];
                } else {
                    $category = $article->category;
                }
            } else {
                $category = $article->category;
            }
            $getAuthorTrans = $this->authorTranslation($article->author_id, $article->lang_code);
            if (!empty($getAuthorTrans)) {
                $article_author = $getAuthorTrans->user_name;
            } else {
                $article_author = $article->author;
            }
            if ($article->lang_code != 1) {
                $menus = [];
            }
            
            $articleStreamId = $primaryStream->id ?? $article->stream_id;
            $questionSets =  $this->articleService->getArticlePracticeDetailSubTopicQues($article, $article_subpage_section_id, $article_subpage_section_topic_id, $articleSubSectionTopic_id) ?? [];
            $sectionData =  $this->articleService->getArticlePracticleSectionData($article, $article_subpage_section_id) ?? [];
            $allSection =  $this->articleService->getArticleAllScetion($article) ?? [];
            $allSectionWidget =  $this->articleService->getArticleAllScetionWidget($article, $article_subpage_section_id) ?? [];
            
            $subTopicData = ArticleSubpageSubsectionSubtopic::find()->where(['id'=>$articleSubSectionTopic_id])->one();
            //echo "<pre>"; print_r($subTopicData); die;
            return [
                'commentModel' => new CommentForm(),
                'tags' => $tags,
                'article' => $article,
                'board' => !empty($board) ? $board : '',
                'author' => $article_author,
                'category' => $category,
                'relatedArticles' => $relatedArticle,
                'trendings' => $this->articleService->getTrendings(10, 'study-abroad', $article->stream_id),
                'recentArticles' => $this->articleService->getRecentArticles(Category::EXCLUDE_CATEGORY, 10, Article::ENTITY_ARTICLE, $article->stream_id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(Article::ENTITY_ARTICLE, $article->id),
                'comments' => $this->articleService->getComments($article->id, Article::ENTITY_ARTICLE),
                'upcomingExams' => $upcomingExams ?? [],
                'popularExams' => $popularExams ?? [],
                'menus' => $menus ?? [],
                'upcomingExamByStream' => $upcomingExamByStream ?? [],
                'primaryStream' => $primaryStream ?? [],
                'streams' => $streams ?? [],
                'intrestedExams' => $intrestedExams ?? [],
                'collegeAcceptingExams' => $collegeAcceptingExams ?? [],
                'checkFilterStatus' => $checkFilterStatus ?? [],
                'liveApplicationForm' => $liveApplicationForm ?? [],
                'entityName' =>  $entityName ?? '',
                'entityId' => $entityId ?? '',
                'entitySlug' => $entitySlug ?? '',
                'entityId' => $entityId ?? '',
                'entityDisplayName' => $entityDisplayName ?? '',
                'courseSpecialization' => $courseSpecialization ?? [],
                'parentCourseName' => $parentCourseName ?? [],
                'coursebyStream' => $coursebyStream ?? [],
                'streamName' => $streamName ?? '',
                'courseCollege' => $courseCollege ?? [],
                'courseExams' => $courseExams ?? [],
                'Chartdata' => $Chartdata ?? [],
                'course' => $course ?? [],
                'stateList' => $stateList ?? [],
                'pages' => $pages ?? [],
                'exam' =>  $exam ?? [],
                'featuredNews' => $featuredNews ?? [],
                'recentNews' => $recentNews ?? [],
                'college' => $college ?? [],
                'nearByCollege' => $nearByCollege ?? [],
                'gallery' => $gallery ?? [],
                'collegeByDiscipline' => $collegeByDiscipline ?? [],
                'showOtherCourseCategory' => $showOtherCourseCategory ?? [],
                'affiliatedCollege' => $affiliatedCollege ?? [],
                'parentCollege' => $parentCollege ?? [],
                // 'reviews' => $reviews,
                // 'revRating' => $revRating,
                // 'revCategoryRating' => $revCategoryRating,
                // 'revDistributionRating'  => $revDistributionRating,
                'productArticle' => $productArticle ?? [],
                'productNews' => $productNews ?? [],
                'sponsorClientUrl' => $sponsorClientUrl ?? '',
                'board' => $board ?? [],
                'boardExamList' => $boardExamList ?? [],
                'subject' => $subject ?? [],
                'pageContent' => $pageContent ?? [],
                'boardarticles' => $boardarticles ?? [],
                'boardExams'  => $boardExams ?? [],
                'stateColleges' => $stateColleges ?? [],
                'translation_data' => $active_trans_data,
                'recentStreamNews' => $this->newsService->getRecent(10, $articleStreamId),
                'dynamicCta' => !empty($dynamicCta) ?  $dynamicCta : (UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_ARTICLE, $article->id, $article->title, $article->slug) ?? []),
                'articleSection' => $this->articleService->getArticlePracticeDetail($article) ?? [],
                'questionSets'=>$questionSets,
                'sectionData'=>$sectionData,
                'allSection' =>$allSection,
                'subTopicData'=>$subTopicData ?? [],
                'allSectionWidget'=>$allSectionWidget ?? [],
            ];
        }, 60 * 60 * 3, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
        return $this->render('article-practice-set', $data);
    }

    public function actionCategoryBaseArticle($article)
    {
        if (!$article) {
            throw new NotFoundHttpException();
        }

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'article-detail';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        $topicArticleLink=[];
        // yii::$app->cache->flush();
        //  Yii::$app->cache->delete($key);
        $data = Yii::$app->cache->getOrSet($key, function () use ($article) {
            if ($data = $this->articleService->getAdTargetData($article)) {
                Ad::setTargetArticleNews($data);
            }
            $tags = $article->tags;
            $recentEntity = $article['id'];
            $entityName = '';
            $entityDisplayName = '';
            $allBoardPage = [];
            if (!empty($article->audio)) {
                $getS3Audio = DataHelper::s3Path($article->audio, 'article_audio', true);
                $article->audio = $getS3Audio;
            }
            $articleSection = $this->articleService->getArticlePracticeDetail($article) ?? [];
            $relatedArticle = $this->articleService->getByCategory($article->category->id) ?? [];
            $active_trans_data = [];
            foreach ($article->activeTranslation as $trans_data) {
                $active_trans['cu_lang'] = DataHelper::getLangCode($article['lang_code']);
                $active_trans['slug'] = $trans_data['slug'];
                $active_trans['lang'] = DataHelper::getLangCode($trans_data['lang_code']);
                $active_trans_data[] = $active_trans;
            }
            if ($article->college) {
                $entityName = 'colleges';
                $college = College::find()->where(['=', 'id', $article->college[0]['id']])->one();
                if (empty($college)) {
                    return [];
                }
                $entityId = $article->college[0]['id'];
                $nearByCollege = $this->collegeService->getCollegesByCity($college->city_id, 4, $college->id);
                $gallery  = $this->collegeService->getGallery($college, 4);
                $collegeByDiscipline  = $this->collegeService->getCollegeByDiscipline($college->id, $college->city->id);
                $entityDisplayName = $article->college[0]['name'];
                $entityId = $article->college[0]['id'];
                $menus = $this->collegeService->getMenu($article->college[0]);
                $entitySlug = $article->college[0]['slug'];
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $liveApplicationForm = $this->collegeService->getLiveApplicationFormData($article->college[0]['id']);
                $affiliatedCollege = $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id);
                $parentCollege  = $college->getParent()->one() ?? $college;
                // $reviews = $this->collegeService->getReviewByCategory($college->slug, [1, 2, 3, 4, 5, 7, 8,]);
                // $revRating = $this->collegeService->getReviewRating($college->old_id);
                // $revCategoryRating = $this->collegeService->getRevCategoryRating($college->old_id);
                // $revDistributionRating  = $this->collegeService->getRevDistributionRating($college->old_id);
                $productArticle = $college->article;
                $productNews = $college->news;
                $sponsorClientUrl = DataHelper::getRedirectionLink('college', $college->id);
                // $recentEntity = $college->id;
            } elseif ($article->exam) {
                $entityName = 'exams';
                $exam = Exam::find()->where(['=', 'id', $article->exam[0]['id']])->one();
                if (empty($exam)) {
                    return [];
                }
                $checkFilterStatus = $this->collegeService->checkFilterPageStatus([], '', '', '', $article->exam[0]->slug);
                $entityId = $article->exam[0]['id'];
                $entitySlug = $article->exam[0]['slug'];
                $entityDisplayName = $article->exam[0]['name'];
                $entityId = $article->exam[0]['id'];
                $liveApplicationForm =  $this->examService->getLiveApplicationFormData($article->exam[0]);
                $pages =  $this->examService->getPages($article->exam[0]['id']);
                $menus = array_column(ArrayHelper::toArray($pages), 'slug');
                $popularExams = $this->examService->popularExams($article->exam[0]['id']);
                $primaryStream   = $article->exam[0]->primaryStream;
                $upcomingExamByStream = isset($primaryStream->id) ? $this->commonExamService->upcomingExams($primaryStream->id) : [];
                if (count($article->exam[0]->streams)) {
                    $streams = $article->exam[0]->streams;
                }
                $streamId = $article->exam[0]->streams[0]->id ?? '';
                $popularExamsIds = array_column($popularExams, 'id');
                $notIn = array_merge($popularExamsIds, array_column($upcomingExamByStream, 'id'));
                $intrestedExams = $this->commonExamService->interestedArticleExams($streamId, $notIn, 4);
                $collegeAcceptingExams = $this->commonExamService->getCollegeAcceptingExams($article->exam[0]['id'], $article->exam[0]['slug']);
                $upcomingExams = $this->examService->upcomingExams(null, 7);
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $productArticle = $exam->article;
                $productNews = $exam->news;
                $sponsorClientUrl =  DataHelper::getRedirectionLink('exam', $exam->id);
                if (empty($article->articleSection)) {
                    $topicArticleLink = $this->examService->getArticleTopicPageNormalArticle($article->exam[0]->slug, $article->id);
                }
                $downloadableResource = $this->examService->getDownloadableResource($exam->id, $exam->primary_stream_id, 'exam');
                // $recentEntity = $exam->id;
            } elseif ($article->course) {
                $course = Course::find()->where(['=', 'id', $article->course[0]['id']])->one();
                if (empty($course)) {
                    return [];
                }
                $parentCourse = $article->course[0]->getParent()->one();
                $parentCourseName = $parentCourse->name ?? $article->course[0]['name'];
                $entityName = 'courses';
                $entityDisplayName = $article->course[0]['short_name'] ?? $article->course[0]['name'];
                $entityId = $article->course[0]['id'];
                $entityDisplayName = $article->course[0]['short_name'];
                $entitySlug = $article->course[0]['slug'];
                $menus =   $this->courseService->getMenu($article->course[0]['id']);
                $liveApplicationForm =  $this->courseService->getLiveApplication($article->course[0]['short_name'], 5);
                $courseSpecialization = $this->courseService->getParentSpecialization($course);
                $coursebyStream = $this->courseService->getStreamCourse($course);
                $streamName = $course->stream->name;
                $courseCollege =  $this->courseService->getTopColleges($course);
                $courseExams = $this->courseService->getExamByCourse($course);
                $Chartdata = $this->courseService->getChartData($course);
                $stateList = $this->courseService->getCourseByState($course);
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $showOtherCourseCategory = true;
                $productArticle = $course->article;
                $productNews = $course->news;
                // $recentEntity = $course->id;
            } elseif ($article->board) {
                $board = Board::find()->where(['=', 'id', $article->board[0]['id']])->one();
                if (empty($board)) {
                    return [];
                }
                $entityId = $article->board[0]['id'];
                $boardExamList = $this->boardService->getExam($board->id);
                $content = $this->boardService->getContent($board->id, 'overview');
                $subject = $this->boardService->getBoardSubject($content->id);
                $sponsorClientUrl = DataHelper::getRedirectionLink('board', $board->id);
                $pageContent = $content;
                $content = BoardHelper::parseBoardContent($content->content);
                // $pages = $this->boardService->getPages($board->id);
                $boardarticles = $this->articleService->getByCategory(17);
                $boardExams  = $this->newsService->getCategoryNews(7);
                $stateColleges = $this->boardService->getCollegesByState($board->state_id);
                $featuredNews = $this->newsService->getFeaturedNews(9, 10);
                $recentNews = $this->newsService->getRecent(10);
                $menus =   $this->boardService->getMenu($board->id);
                $entityName = 'boards';
                $entityDisplayName = $article->board[0]['display_name'] ?? $article->board[0]['name'];
                $entityId = $article->board[0]['id'];
                $entitySlug = $article->board[0]['slug'];
                $productArticle = $board->article;
                $productNews = $board->news;
                $allBoardPage = $this->boardService->getAllBoardPage();
                // $recentEntity = $board->id;
            }
            if ($article['lang_code'] != 1) {
                $category_trns = $article->translatedCategory;
                if (!empty($category_trns)) {
                    $category = $article->category;
                    $category['name'] = $category_trns['name'];
                    $category['display_name'] = $category_trns['display_name'];
                    $category['description'] = $category_trns['description'];
                    $category['h1'] = $category_trns['h1'];
                    $category['meta_title'] = $category_trns['meta_title'];
                    $category['meta_description'] = $category_trns['meta_description'];
                } else {
                    $category = $article->category;
                }
            } else {
                $category = $article->category;
            }
            $getAuthorTrans = $this->authorTranslation($article->author_id, $article->lang_code);
            if (!empty($getAuthorTrans)) {
                $article_author = $getAuthorTrans->user_name;
                // $article_author = $article->author;
            } else {
                $article_author = $article->author;
            }
            if ($article->lang_code != 1) {
                $menus = [];
            }
            
            $articleStreamId = $primaryStream->id ?? $article->stream_id;
            return [
                'commentModel' => new CommentForm(),
                'tags' => $tags,
                'article' => $article,
                'board' => !empty($board) ? $board : '',
                'author' => $article_author,
                'category' => $category,
                'relatedArticles' => $relatedArticle,
                'trendings' => $this->articleService->getTrendings(10, 'study-abroad', $article->stream_id),
                'recentArticles' => $this->articleService->getRecentArticles(Category::EXCLUDE_CATEGORY, 10, Article::ENTITY_ARTICLE, $article->stream_id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(Article::ENTITY_ARTICLE, $article->id),
                'comments' => $this->articleService->getComments($article->id, Article::ENTITY_ARTICLE),
                'upcomingExams' => $upcomingExams ?? [],
                'popularExams' => $popularExams ?? [],
                'menus' => $menus ?? [],
                'upcomingExamByStream' => $upcomingExamByStream ?? [],
                'primaryStream' => $primaryStream ?? [],
                'streams' => $streams ?? [],
                'intrestedExams' => $intrestedExams ?? [],
                'collegeAcceptingExams' => $collegeAcceptingExams ?? [],
                'checkFilterStatus' => $checkFilterStatus ?? [],
                'liveApplicationForm' => $liveApplicationForm ?? [],
                'entityName' =>  $entityName ?? '',
                'entityId' => $entityId ?? '',
                'entitySlug' => $entitySlug ?? '',
                'entityId' => $entityId ?? '',
                'entityDisplayName' => $entityDisplayName ?? '',
                'courseSpecialization' => $courseSpecialization ?? [],
                'parentCourseName' => $parentCourseName ?? [],
                'coursebyStream' => $coursebyStream ?? [],
                'streamName' => $streamName ?? '',
                'courseCollege' => $courseCollege ?? [],
                'courseExams' => $courseExams ?? [],
                'Chartdata' => $Chartdata ?? [],
                'course' => $course ?? [],
                'stateList' => $stateList ?? [],
                'pages' => $pages ?? [],
                'exam' =>  $exam ?? [],
                'featuredNews' => $featuredNews ?? [],
                'recentNews' => $recentNews ?? [],
                'college' => $college ?? [],
                'nearByCollege' => $nearByCollege ?? [],
                'gallery' => $gallery ?? [],
                'collegeByDiscipline' => $collegeByDiscipline ?? [],
                'showOtherCourseCategory' => $showOtherCourseCategory ?? [],
                'affiliatedCollege' => $affiliatedCollege ?? [],
                'parentCollege' => $parentCollege ?? [],
                // 'reviews' => $reviews,
                // 'revRating' => $revRating,
                // 'revCategoryRating' => $revCategoryRating,
                // 'revDistributionRating'  => $revDistributionRating,
                'productArticle' => $productArticle ?? [],
                'productNews' => $productNews ?? [],
                'sponsorClientUrl' => $sponsorClientUrl ?? '',
                'board' => $board ?? [],
                'boardExamList' => $boardExamList ?? [],
                'subject' => $subject ?? [],
                'pageContent' => $pageContent ?? [],
                'boardarticles' => $boardarticles ?? [],
                'boardExams'  => $boardExams ?? [],
                'stateColleges' => $stateColleges ?? [],
                'translation_data' => $active_trans_data,
                'recentStreamNews' => $this->newsService->getRecent(10, $articleStreamId),
                'dynamicCta' => !empty($dynamicCta) ?  $dynamicCta : (UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_ARTICLE, $article->id, $article->title, $article->slug) ?? []),
                'articleSection' => $this->articleService->getArticlePracticeDetail($article) ?? [],
                'topicArticleLink'=>$topicArticleLink ?? [],
                'allBoardPage' => $allBoardPage ?? [],
                'downloadableResource' => $downloadableResource ?? []
                // 'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Article::ENTITY_ARTICLE, $recentEntity), $entityDisplayName]
            ];
        }, 60 * 60 * 3, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
            return $this->render('detail', $data);
    }
}
