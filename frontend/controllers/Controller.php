<?php

namespace frontend\controllers;

use frontend\helpers\Url;
use Yii;
use yii\base\Theme;
use yii\caching\TagDependency;
use common\helpers\DataHelper;

class Controller extends \yii\web\Controller
{
    public $ogImage;

    public $ogType;

    public $ogTitle;


    public $description;

    public $ampCss;


    public function beforeAction($action)
    {
        // dd(Yii::$app->request->get('amp/boards'));
        Yii::$app->language = Yii::$app->request->get('lang', 'en');
        $currentUrl = Yii::$app->request->getAbsoluteUrl();
        if (Yii::$app->request->get('amp') == 'amp') {
            $this->setTheme('amp');
        }

        if (Yii::$app->request->get('/amp/boards') == 'amp' || strpos($currentUrl, '/amp/boards') !== false) {
            $this->setTheme('amp');
        }

        if (Yii::$app->request->get('cache-tag')) {
            $this->invalidateTagDependency(Yii::$app->request->get('cache-tag'));
        }

        if (Yii::$app->request->get('cache')) {
            $this->invalidateTagDependency(DataHelper::generateCacheKey());
        }

        $this->redirectToSmall();
        // dd($action);
        return parent::beforeAction($action);
    }


    protected function setTheme($theme)
    {
        Yii::$app->view->theme = new Theme([
            'basePath' => "@app/themes/{$theme}",
            'baseUrl' => '@web/v2',
            'pathMap' => ['@app/views' => "@app/themes/{$theme}"]
        ]);
    }

    /**
     * Redirect if url contains upper case letter
     */
    private function redirectToSmall()
    {
        $currentUrl = strtr('scheme://hostpath', parse_url(Yii::$app->request->getAbsoluteUrl()));
        $newUrl = strtolower($currentUrl);

        if ($currentUrl !== $newUrl && Yii::$app->request->isGet) {
            if (!empty($_SERVER['QUERY_STRING'])) {
                $newUrl .= '?' . $_SERVER['QUERY_STRING'];
            }
            Yii::$app->response->redirect($newUrl, 301);
        }

        if (Yii::$app->request->get('cache-tag')) {
            $this->redirectToCurrentUrl();
        }
    }

    public function getHash($args = [])
    {
        $hash = md5(base64_encode(serialize($args)));

        return Yii::$app->controller->id . '-' . Yii::$app->controller->action->id . '-' . $hash;
    }

    public function invalidateTagDependency($tag)
    {
        TagDependency::invalidate(Yii::$app->cache, $tag);
    }

    public function redirectToCurrentUrl()
    {
        $currentUrl = Yii::$app->request->getAbsoluteUrl();
        if (Yii::$app->request->get('cache-tag') && Yii::$app->request->get('page')) {
            $url = str_replace('?' . $_SERVER['QUERY_STRING'], '', $currentUrl);
            $redirectUrl = $url . '?page=' . Yii::$app->request->get('page');
            return $this->redirect($redirectUrl);
        }
    }
}
