<?php

use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\assets\AppAsset;
use yii\helpers\BaseStringHelper;
use common\models\Article;
use common\helpers\DataHelper;
use frontend\helpers\Freestartads;

//utils
$this->title = $indexTitle;
$this->context->description = $indexDescription;
$isMobile = \Yii::$app->devicedetect->isMobile();

if (Yii::$app->language == 'en') {
    if (!empty($cate->slug)) {
        $url = $cate->slug;
    } else {
        $url = '/articles';
    }
} else {
    $url = '/' . Yii::$app->language . '/articles';
}
$this->registerLinkTag(['href' =>  Url::base(true) . $url, 'rel' => 'alternate', 'hreflang' => Yii::$app->language]);

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
if (!empty($cate->slug)) {
    $this->params['breadcrumbs'][] = ['label' => $cate->name, 'url' => [$url], 'title' => $cate->name];
}
$this->params['breadcrumbs'][] = Yii::t('app', 'Articles');

$this->params['previous_url'] = Yii::$app->request->referrer;
$this->params['page_url'] = Yii::$app->request->url;
$this->params['entity_name'] = 'Articles-Listing-Page';
$this->params['entity'] = Article::ENTITY_ARTICLE;
$this->params['entity'] = Article::ENTITY_ARTICLE;
$this->params['canonicalUrl'] = Url::base(true) . $url;

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'article_landing.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'bottom-widget.css', ['depends' => [AppAsset::class]]);

?>

<main>
    <div class="bannerSection">
        <h1><?= Yii::t('app', 'Latest Education Articles'); ?></h1>
        <div class="latestArticleSection row">
            <article class="articleDisplay">
                <?php $latestArticleIterationCount = 1; ?>
                <?php foreach ($latestArticles as $article):
                    if (empty($article)) {
                        return '';
                    }
                    if (empty($article->author)) {
                        $author = $article->defaultuser;
                    } else {
                        $author = $article->author;
                    }
                    if (strpos($article->title, '{current-year}') !== false) { //first we check if the url contains the string '{current-year}'
                        $article->title = str_replace('{current-year}', date('Y'), $article->title); //if yes, we simply replace it with en
                    }
                    ?>
                    <div class="<?= 'article' . $latestArticleIterationCount . '-view' ?> <?= $latestArticleIterationCount > 1 ? 'display_none' : '' ?>">
                        <figure>
                            <?php if ($article->country_slug == null): ?>
                                <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toArticleDetail($article->slug, DataHelper::getLangCode($article->lang_code), $cate->slug ?? '') ?>')" data-src="<?= $article->cover_image ? ArticleDataHelper::getImage($article->cover_image) : ArticleDataHelper::getImage(); ?>" src="<?= $article->cover_image ? ArticleDataHelper::getImage($article->cover_image) : ArticleDataHelper::getImage(); ?>" alt="<?= $article->title ?? '' ?>" width="463" height="325" />
                            <?php else: ?>
                                <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toCountryDetail($article->country_slug, $article->slug) ?>')" data-src="<?= $article->cover_image ? Url::getStudyAbroadImage($article->cover_image) : Url::getStudyAbroadImage() ?>" src="<?= $article->cover_image ? Url::getStudyAbroadImage($article->cover_image) : Url::getStudyAbroadImage() ?>" alt="<?= $article->title ?? '' ?>" width="463" height="325" />
                            <?php endif; ?>
                        </figure>

                        <div class="aticleInfo">
                            <h2>
                                <a href="<?= empty($article->country_slug) ? Url::toArticleDetail($article->slug, DataHelper::getLangCode($article->lang_code), $cate->slug ?? '') : Url::toCountryDetail($article['country_slug'], $article['slug']) ?>" title="<?= $article->title ?>"> <?= $article->title ?? '' ?></a>
                            </h2>
                            <div class="updated-info row">
                                <div class="updatedBy">
                                    <?php if (!empty($author->profile->about)): ?>
                                        <a class="authorName" href="<?= Url::toAllAuthorPost($author->slug) ?>" title="<?= $author->name ?>"><?= $author->name ?></a>
                                    <?php else: ?>
                                        <p class="authorNameText"><?= $author->name ?></p>
                                    <?php endif; ?>
                                </div>
                                <?php if (!$isMobile): ?>
                                    <p><?= Yii::$app->formatter->asDate($article->updated_at ?? 'today') ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php $latestArticleIterationCount++ ?>
                <?php endforeach; ?>
            </article>
            <div class="articleList">
                <ul>
                    <?php $linkIterationCount = 1 ?>
                    <?php foreach ($latestArticles as $link):
                        if (empty($link)) {
                            return '';
                        } ?>
                        <li class="<?= 'article' . $linkIterationCount ?>">
                            <a href="<?= empty($link->country_slug) ? Url::toArticleDetail($link->slug, DataHelper::getLangCode($link->lang_code), $cate->slug ?? '') : Url::toCountryDetail($link->country_slug, $link->slug) ?>" title="<?= $link->h1 ?>">
                                <?php if ($link->country_slug == null): ?>
                                    <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toArticleDetail($link->slug, DataHelper::getLangCode($link->lang_code, $cate->slug ?? '')) ?>')" data-src="<?= $link->cover_image ? ArticleDataHelper::getImage($link->cover_image) : ArticleDataHelper::getImage() ?>" src="<?= $link->cover_image ? ArticleDataHelper::getImage($link->cover_image) : ArticleDataHelper::getImage() ?>" alt="<?= $link->h1 ?>" width="463" height="325" />
                                <?php else: ?>
                                    <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toCountryDetail($link->country_slug, $link->slug) ?>')" data-src="<?= $link->cover_image ? Url::getStudyAbroadImage($link->cover_image) : Url::getStudyAbroadImage() ?>" src="<?= $link->cover_image ? Url::getStudyAbroadImage($link->cover_image) : Url::getStudyAbroadImage() ?>" alt="<?= $link->h1 ?>" width="463" height="325" />
                                <?php endif; ?>
                                <div class="articleName">
                                    <?= !empty($link->title) ? $link->title : '' ?>
                                </div>
                            </a>
                        </li>
                        <?php $linkIterationCount++ ?>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>

    <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="<?= $isMobile ? 'height: 50px;' : '' ?>background:#EAEAEA;">
                    <?php /*if ($isMobile) : ?>
                        <?php echo Ad::unit('GMU_ARTICLES_LANDING_WAP_300x50_ATF', '[300,50]') ?>
                    <?php else : */ ?>
                    <?php echo Ad::unit('GMU_ARTICLES_LANDING_WEB_728x90_ATF', '[728,90]') ?>
                    <?php //endif;
                    ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>

    <?php if (!empty($trendings)): ?>
        <section class="latestInfoSection">
            <h2 class="row"><?= Yii::t('app', 'TRENDING ARTICLES'); ?></h2>
            <div class="latestInfoList row">
                <?php foreach ($trendings as $trending):
                    $about = $trending->lang_code == 1
                        ? $trending->author->profile->about ?? null
                        : $trending->transAuthor->profile->about ?? null;
                    $slug = ($trending->lang_code == 1) ? $trending->author->slug : $trending->transAuthor->slug;

                    $articleSlug = empty($trending->country_slug) ? Url::toArticleDetail($trending->slug, DataHelper::getLangCode($trending->lang_code), $cate->slug ?? '') : Url::toCountryDetail($trending->country_slug, $trending->slug);
                    if ($trending->lang_code != 1) {
                        $author_name =  isset($trending->transAuthor) ? $trending->transAuthor->user_name : '';
                    } else {
                        $author_name =  isset($trending->author) ? $trending->author->name : '';
                    }
                    ?>
                    <?php if (empty($trending)) {
                        return '';
                    } ?>
                    <article class="latestInfoDiv">
                        <a href="<?= $articleSlug ?>" title="<?= $trending->h1 ?>">
                            <figure>
                                <?php if ($trending->country_slug == null): ?>
                                    <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toArticleDetail($trending->slug, DataHelper::getLangCode($trending->lang_code), $cate->slug ?? '') ?>')" data-src=" <?= ArticleDataHelper::getImage($trending->cover_image) ?>" src="<?= ArticleDataHelper::getImage($trending->cover_image) ?>" alt="<?= $trending->title ?>" />
                                <?php else: ?>
                                    <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toCountryDetail($article->country_slug, $article->slug) ?>')" data-src=" <?= $trending->cover_image ? Url::getStudyAbroadImage($trending->cover_image) : Url::getStudyAbroadImage() ?>" src="<?= $trending->cover_image ? Url::getStudyAbroadImage($trending->cover_image) : Url::getStudyAbroadImage() ?>" alt="<?= $trending->title ?>" />
                                <?php endif; ?>
                            </figure>
                        </a>
                        <div class="latestInfoTxt">
                            <h3>
                                <a href="<?= $articleSlug ?>" title="<?= $trending->h1 ?>">
                                    <?= BaseStringHelper::truncate($trending->h1, 90) ?? '' ?></a>
                            </h3>
                            <?php if (!empty($about)): ?>
                                <a class="trendingAuthorName"
                                    href="<?= Url::toAllAuthorPost($slug) ?>" title="<?= $author_name ?>"><?= $author_name ?></a>
                            <?php else: ?>
                                <p style="transform: translateY(-15px);"><?= $author_name ?></p>
                            <?php endif; ?>
                        </div>
                    </article>
                <?php endforeach; ?>
            </div>
        </section>
    <?php endif; ?>
    <section class="articleSection">
        <h2><?= Yii::t('app', 'BROWSE ARTICLES'); ?></h2>
        <div class="articleRelataedLinks">
            <p class="btn_left over">
                <i class="spriteIcon left_angle"></i>
            </p>
            <p class="btn_right">
                <i class="spriteIcon right_angle"></i>
            </p>
            <ul>
                <?php $categoryCount = 0; ?>
                <?php foreach ($byCategories as $category):
                    if (!empty($category['articles'])) { ?>
                        <li>
                            <a class="<?= $categoryCount == 0 ? 'activeLink' : '' ?>" href="javascript:;" data-tab="<?= $category['slug'] ?>" title="<?= $category['name'] ?>"><?= $category['name'] ?></a>
                        </li>
                        <?php $categoryCount++ ?>
                    <?php } ?>
                <?php endforeach; ?>
            </ul>

        </div>
        <div class="generalArticleSection">
            <?php $articleCategoryCount = 0 ?>
            <?php foreach ($byCategories as $articleCategory):
                if (!empty($articleCategory['articles'])) { ?>
                    <div id="<?= $articleCategory['slug'] ?>" class="tab-content <?= $articleCategoryCount == 0 ? 'activeLink' : '' ?>">
                        <div class="articlesByCategory row">
                            <?php $totalArticleDisplayCount = 0;
                            $s3Path = 'https://media.getmyuni.com/yas/images/defaultcardbanner.png'; ?>
                            <?php foreach ($articleCategory['articles'] as $article):
                                $countryDetailUrl = Url::toCountryDetail($article['country_slug'], $article['slug']);
                                $articleDetailUrl = Url::toArticleDetail($article['slug'], DataHelper::getLangCode($article['lang_code']), $cate->slug ?? '');
                                $article_source_img = $article['cover_image'] ? ArticleDataHelper::getImage($article['cover_image']) : ArticleDataHelper::getImage();
                                $country_article_source_img = $article['cover_image'] ? Url::getStudyAbroadImage($article['cover_image']) : Url::getStudyAbroadImage();
                                
                                ?>
                                <?php if ($totalArticleDisplayCount > 11): ?>
                                    <?php break ?>
                                <?php endif; ?>
                                <div class="browsedArticleDiv">
                                    <a href="<?= (empty($article['country_slug'])) ?  $articleDetailUrl : $countryDetailUrl ?>" title="<?= $article['title'] ?>">
                                        <figure>
                                            <?php if ($article['country_slug'] == null): ?>
                                                <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toArticleDetail($article['slug'], DataHelper::getLangCode($article['lang_code'], $cate->slug ?? '')) ?>')" data-src="<?= $article_source_img ?>" src="<?= $article_source_img ?>" alt="<?= $article['title'] ?>" width="237" height="207" />
                                            <?php else: ?>
                                                <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toCountryDetail($article['country_slug'], $article['slug']) ?>')" data-src="<?= $country_article_source_img ?>" src="<?= $article['cover_image'] ? Url::getStudyAbroadImage($article['cover_image']) : $s3Path ?>" alt="<?= $article['title'] ?>" width="237" height="207" />
                                            <?php endif; ?>
                                        </figure>
                                        <div class="browsedArticleText">
                                            <h3>
                                                <?= BaseStringHelper::truncate($article['title'], 90) ?? '' ?>
                                            </h3>
                                            <?php if (isset($article['userStatus']) && $article['userStatus'] == '10'): //10 status active
                                                ?>
                                                <?php if (isset($article['name']) && !empty($article['about'])): ?>
                                                    <a class="authorNameSection" href="<?= Url::toAllAuthorPost($article['user_slug']); ?>" title="<?= $article['name'] ?>"><?= $article['name'] ?></a>
                                                <?php else: ?>
                                                    <p style="margin-top:20px"><?= $article['name'] ?></p>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <a class="authorNameSection" href="<?= Url::toAllAuthorPost($articleCategory['defaultUser']->slug); ?>" title="<?= $articleCategory['defaultUser']->name ?>"><?= $articleCategory['defaultUser']->name ?></a>
                                            <?php endif; ?>
                                        </div>
                                    </a>
                                </div>
                                <?php $totalArticleDisplayCount++ ?>
                            <?php endforeach; ?>
                            <?php if (empty($cate)): ?>
                                <?php if (Yii::$app->language != 'en') { ?>
                                <div class="viewAllArticleDiv" style="width: 100%;">
                                    <a href="<?= Url::toArticleDetail($articleCategory['slug'], Yii::$app->language) ?>" class="primaryBtn" title=" <?= $articleCategory['name'] ?> Articles"><?= Yii::t('app', 'ALL') ?> <?= strtoupper($articleCategory['name']) ?> <?= Yii::t('app', 'ARTICLES') ?> <?= Yii::t('app', 'VIEW') ?></a>
                                </div>
                                <div class="browsedArticleDiv mobileOnly">
                                    <div class="viewAllDiv">
                                        <a href="<?= Url::toArticleDetail($articleCategory['slug'], Yii::$app->language) ?>" title=" <?= $articleCategory['name'] ?> Articles"><i class="spriteIcon viewAllIcon"></i> <?= Yii::t('app', 'ALL') ?> <?= strtoupper($articleCategory['name']) ?> <?= Yii::t('app', 'ARTICLES') ?><?= Yii::t('app', 'VIEW') ?></a>
                                    </div>
                                </div>
                                <?php } else { ?>
                                <div class="viewAllArticleDiv" style="width: 100%;">
                                    <a href="<?= Url::toArticleDetail($articleCategory['slug'], Yii::$app->language) ?>" class="primaryBtn" title=" <?= $articleCategory['name'] ?> Articles"><?= Yii::t('app', 'VIEW ALL') ?> <?= strtoupper($articleCategory['name']) ?> <?= Yii::t('app', 'ARTICLES') ?></a>
                                </div>
                                <div class="browsedArticleDiv mobileOnly">
                                    <div class="viewAllDiv">
                                        <a href="<?= Url::toArticleDetail($articleCategory['slug'], Yii::$app->language) ?>" title=" <?= $articleCategory['name'] ?> Articles"><i class="spriteIcon viewAllIcon"></i> <?= Yii::t('app', 'VIEW ALL') ?> <?= strtoupper($articleCategory['name']) ?> <?= Yii::t('app', 'ARTICLES') ?></a>
                                    </div>
                                </div>
                                <?php } ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php $articleCategoryCount++ ?>
                <?php } ?>

            <?php endforeach; ?>
        </div>

    </section>

    <aside>
        <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background: #eaeaea;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                        ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90')
                        ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </aside>

    <?php /*<div class="setAlarmDiv mobileOnly">
           <?= frontend\helpers\Html::leadButton('<i class="spriteIcon alarmIcon"></i> ARTICLE ALERTS', ['entity' => Lead::ENTITY_ARTICLE, 'entityId' => '', 'ctaLocation' => HelpersLead::getCTAsName(Lead::ENTITY_ARTICLE . '.bottom-sticky-cta'), 'leadformtitle' => 'ARTICLE ALERTS'], ['class' => 'primaryBtn setExamAlert getLeadForm'], 'js-open-lead-form') ?>
        </div>*/ ?>
</main>